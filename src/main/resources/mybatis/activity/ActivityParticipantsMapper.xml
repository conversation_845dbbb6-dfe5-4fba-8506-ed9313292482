<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.activity.mapper.ActivityParticipantsMapper">

    <select id="getParticipantsByActivityId" resultType="com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO">
        SELECT
            ap.id,
            ap.PKID as pkid,
            ap.Activity_PKID as activityPkid,
            ap.Pepole_PKID as pepolePkid,
            ap.Is_Attend as isAttend,
            CASE ap.Is_Attend
                WHEN '1' THEN '参加'
                WHEN '0' THEN '不参加'
                WHEN '2' THEN '请假'
                ELSE ''
            END as attendStatusText,
            ap.No_Attend_Reason as noAttendReason,
            ap.Create_ID as createId,
            ap.Create_Time as createTime,
            ap.Update_ID as updateId,
            ap.Update_Time as updateTime,
            ap.is_leader as is<PERSON>eader,
            ap.sort,
            ap.region_id as regionId,
            cm.user_name as peopleName,
            cm.unit_post as unitPost
        FROM
            activity_participants ap
        LEFT JOIN (
            SELECT
                scm.id,
                scm.user_name,
                scm.unit_post
            FROM
                sys_committee_member scm
            WHERE
                (scm.id, scm.year) IN (
                    SELECT
                        id,
                        MAX(year) as max_year
                    FROM
                        sys_committee_member
                    GROUP BY
                        id
                )
        ) cm ON CAST(ap.Pepole_PKID AS UNSIGNED) = cm.id
        WHERE
            ap.Activity_PKID = #{activityPkid}
        ORDER BY
            ap.sort ASC
    </select>

    <select id="getParticipantDetailByActivityAndPeople" resultType="com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO">
        SELECT
            ap.id,
            ap.PKID as pkid,
            ap.Activity_PKID as activityPkid,
            ap.Pepole_PKID as pepolePkid,
            ap.Is_Attend as isAttend,
            CASE ap.Is_Attend
                WHEN '1' THEN '参加'
                WHEN '0' THEN '不参加'
                WHEN '2' THEN '请假'
                ELSE ''
            END as attendStatusText,
            ap.No_Attend_Reason as noAttendReason,
            ap.Create_ID as createId,
            ap.Create_Time as createTime,
            ap.Update_ID as updateId,
            ap.Update_Time as updateTime,
            ap.is_leader as isLeader,
            ap.sort,
            ap.region_id as regionId,
            cm.user_name as peopleName,
            cm.unit_post as unitPost
        FROM
            activity_participants ap
        LEFT JOIN (
            SELECT
                scm.id,
                scm.user_name,
                scm.unit_post
            FROM
                sys_committee_member scm
            WHERE
                (scm.id, scm.year) IN (
                    SELECT
                        id,
                        MAX(year) as max_year
                    FROM
                        sys_committee_member
                    GROUP BY
                        id
                )
        ) cm ON ap.Pepole_PKID = CAST(cm.id AS CHAR)
        WHERE
            ap.Activity_PKID = #{activityPkid}
            AND ap.Pepole_PKID = #{pepolePkid}
        LIMIT 1
    </select>
</mapper>
