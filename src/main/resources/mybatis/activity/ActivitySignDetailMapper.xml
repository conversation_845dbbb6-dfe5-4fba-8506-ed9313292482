<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.activity.mapper.ActivitySignDetailMapper">
    <select id="getSignDetailPageWithName" resultType="com.ruoyi.project.activity.domain.vo.ActivitySignDetailVO">
        SELECT
            asd.id,
            asd.pkid,
            asd.sign_pkid AS signPkid,
            asd.Pepole_PKID AS pepolePkid,
            '' AS peopleName,
            '' AS unitPost,
            asd.begin_date AS beginDate,
            asd.end_date AS endDate,
            asd.is_sign AS isSign,
            CASE WHEN asd.is_sign = '1' THEN '已签到' ELSE '未签到' END AS signStatusText,
            asd.sign_type AS signType,
            asd.is_leave AS isLeave,
            CASE WHEN asd.is_leave = '1' THEN '已请假' ELSE '未请假' END AS leaveStatusText,
            asd.reason,
            asd.is_leader AS isLeader,
            (SELECT tas.Sign_BeginDate FROM activity_sign tas WHERE tas.id = asd.sign_pkid) AS signBeginDate,
            (SELECT tas.Sign_EndDate FROM activity_sign tas WHERE tas.id = asd.sign_pkid) AS signEndDate
        FROM
            activity_sign_detail asd
        WHERE
            asd.sign_pkid = #{signPkid}
        <if test="peopleName != null and peopleName != ''">
            AND asd.Pepole_PKID IN (
                SELECT CAST(cm.id AS CHAR)
                FROM sys_committee_member cm
                WHERE cm.user_name LIKE CONCAT('%', #{peopleName}, '%')
            )
        </if>
        ORDER BY
            asd.id DESC
    </select>
    <select id="getCurrentUserActivitySignDetails" resultType="com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO">
        SELECT
            asd.id,
            a.title,
            as2.Sign_BeginDate AS signBegindate,
            as2.Sign_EndDate AS signEnddate,
            asd.begin_date AS beginDate,
            as2.pepole_name AS pepoleName,
            asd.is_sign AS isSign,
            asd.sign_type AS signType,
            asd.is_leave AS isLeave,
            as2.reason
        FROM
            activity_sign_detail asd
        LEFT JOIN activity_sign as2 ON asd.sign_pkid = as2.id
        LEFT JOIN activity_basicinfo a ON asd.activity_pkid = a.id
        WHERE
            asd.activity_pkid = #{activityPkid}
            AND asd.Pepole_PKID = #{userId}
        <if test="signDesc != null and signDesc != ''">
            AND as2.reason like CONCAT('%', #{signDesc}, '%')
        </if>    
        ORDER BY
            asd.begin_date DESC
    </select>
    <select id="getUserActivitySignDetails" resultType="com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO">
        SELECT
	        asd.id,
	        ab.Title,
	        tas.Sign_BeginDate AS signBegindate,
	        tas.Sign_EndDate AS signEnddate,
	        tas.Pepole_name AS pepoleName,
	        tas.Pepole_name AS sponsor,
	        tas.Reason AS signDesc,
	        tas.id AS signId,
	        tas.PKID AS signPkid,
	        asd.Is_Sign AS isSign,
	        asd.Sign_Type AS signType,
	        asd.Is_Leave AS isLeave,
	        asd.Reason AS leaveReason,
            asd.Begin_Date AS signDate,
            asd.Begin_Date AS Begin_Date
        FROM
        	activity_basicinfo ab
        	LEFT JOIN activity_sign tas ON ab.id = tas.Activity_PKID
        	LEFT JOIN activity_sign_detail asd ON tas.PKID = asd.sign_pkid 
        WHERE
        	ab.id =  #{activityPkid}
            AND asd.Pepole_PKID IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="signDesc != null and signDesc != ''">
            AND tas.reason like CONCAT('%', #{signDesc}, '%')
        </if>    
        ORDER BY
            tas.Sign_BeginDate
    </select>
</mapper>