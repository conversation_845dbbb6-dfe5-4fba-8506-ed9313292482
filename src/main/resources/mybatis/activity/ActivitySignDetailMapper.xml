<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.activity.mapper.ActivitySignDetailMapper">

    <select id="getSignDetailPageWithName" resultType="com.ruoyi.project.activity.domain.vo.ActivitySignDetailVO">
        SELECT
        asd.id,
        asd.pkid,
        asd.sign_pkid AS signPkid,
        asd.Pepole_PKID AS pepolePkid,
        cm.user_name AS peopleName,
        cm.unit_post AS unitPost,
        asd.begin_date AS beginDate,
        asd.end_date AS endDate,
        asd.is_sign AS isSign,
        CASE WHEN asd.is_sign = '1' THEN '已签到' ELSE '未签到' END AS signStatusText,
        asd.sign_type AS signType,
        asd.is_leave AS isLeave,
        CASE WHEN asd.is_leave = '1' THEN '已请假' ELSE '未请假' END AS leaveStatusText,
        asd.reason,
        asd.is_leader AS isLeader
        FROM
        activity_sign_detail asd
        LEFT JOIN sys_committee_member cm ON asd.Pepole_PKID = cm.id COLLATE utf8mb4_unicode_ci
        WHERE
        asd.sign_pkid = #{signPkid} COLLATE utf8mb4_unicode_ci
        <if test="peopleName != null and peopleName != ''">
            AND u.nick_name COLLATE utf8mb4_unicode_ci LIKE CONCAT('%', #{peopleName}, '%')
        </if>
        ORDER BY
        asd.begin_date DESC
    </select>
    
    <select id="getCurrentUserActivitySignDetails" resultType="com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO">
        SELECT
            asd.id,
            a.title,
            as2.Sign_BeginDate AS signBegindate,
            as2.Sign_EndDate AS signEnddate,
            asd.begin_date AS beginDate,
            as2.pepole_name AS pepoleName,
            asd.is_sign AS isSign,
            asd.sign_type AS signType,
            asd.is_leave AS isLeave,
            as2.reason
        FROM
            activity_sign_detail asd
        LEFT JOIN activity_sign as2 ON asd.sign_pkid = as2.id
        LEFT JOIN activity_basicinfo a ON asd.activity_pkid = a.id
        WHERE
            asd.activity_pkid = #{activityPkid}
            AND asd.Pepole_PKID = #{userId}
        <if test="signDesc != null and signDesc != ''">
            AND as2.reason like CONCAT('%', #{signDesc}, '%')
        </if>    
        ORDER BY
            asd.begin_date DESC
    </select>
</mapper>
