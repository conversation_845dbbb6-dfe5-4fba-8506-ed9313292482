<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper">

    <select id="handleAbsencePlenaryUnexcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = false AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
          AND YEAR(m.start_time) = #{member.year}
          AND m.meeting_type = 'FULL_COMM'
          AND sd.is_sign = false
    </select>

    <select id="handleAbsencePlenaryExcused" resultType="java.lang.Integer" >
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'FULL_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceStandingUnexcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = false AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'STANDING_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceStandingExcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'STANDING_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceCommitteeUnexcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = false AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'SPEC_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceCommitteeExcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'SPEC_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAttendanceStandingExtended" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'EXP_STAND_COMM'
            AND sd.is_sign = true
    </select>

    <select id="handleAttendanceCommitteeOther" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'OTHER'
            AND sd.is_sign = true
    </select>

    <select id="handleAttendanceHigherMeeting" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type IN ('TOPIC_CONSULT', 'TARGET_CONSULT', 'PROP_HANDLE_FORUM')
            AND sd.is_sign = true
    </select>

    <select id="hasNotSubmittedProposalWithinOneYear" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM proposal p
                INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id
            WHERE p.del_flag = false
                AND pur.del_flag = false
                AND pur.proposer_id = #{member.userId}
                AND p.year = #{member.year}
        )
    </select>

    <select id="countApprovedProposalsAsPrimaryProposer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM proposal p
            INNER JOIN proposal_user_rel pur ON
                p.id = pur.proposal_id AND
                pur.del_flag = false AND
                pur.proposer_id = #{member.userId} AND
                pur.submit_type = '领衔'
        WHERE p.del_flag = false
            AND p.submit_type = 'INDIVIDUAL'
            AND p.year = #{member.year}
            AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
    </select>

    <select id="countApprovedProposalsAsSecondaryProposer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM proposal p
            INNER JOIN proposal_user_rel pur ON
                p.id = pur.proposal_id AND
                pur.del_flag = false AND
                pur.proposer_id = '附议'
        WHERE p.del_flag = false
            AND p.submit_type = 'INDIVIDUAL'
            AND p.year = #{member.year}
            AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
    </select>

    <select id="hasNotSubmittedManuscriptWithinOneYear" resultType="java.lang.Boolean">
        SELECT EXISTS (
            SELECT 1
            FROM proposal p
                INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id
            WHERE p.del_flag = false
                AND pur.del_flag = false
                AND pur.proposer_id = #{member.userId}
                AND p.year = #{member.year}
        )
    </select>

    <select id="countAcceptedManuscripts" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
            INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
        WHERE m.del_flag = false
            AND m.status != 'DISCARD'
            AND m.adopt_way != 'NOT_ADOPT'
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
    </select>

    <select id="countDistrictEndorsedManuscript" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
        INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
        INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false
        WHERE m.del_flag = false
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
            AND me.endorse_type = 'DISTRICT'
    </select>


    <select id="countCityEndorsedManuscript" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
            INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
            INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false
        WHERE m.del_flag = false
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
            AND FIND_IN_SET('CITY', me.endorse_type) > 0
            AND FIND_IN_SET('PROVINCE', me.endorse_type) &lt; 0
    </select>

    <select id="countProvinceEndorsedManuscript" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
            INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
            INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false
        WHERE m.del_flag = false
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
            AND FIND_IN_SET('PROVINCE', me.endorse_type) > 0
    </select>

</mapper>