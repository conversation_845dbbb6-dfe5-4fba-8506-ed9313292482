<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalStatisticsMapper">

    <select id="selectResultStatistics" resultType="com.ruoyi.project.proposal.domain.ProposalHandle">
        SELECT ph.*
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
        WHERE p.del_flag = false
            AND EXTRACT(year FROM ph.create_time) BETWEEN #{year} AND #{year}
    </select>

    <select id="analyzeDeptProposalCount"
            resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.serial_number, 4, '0') AS serial_number,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id AND pu.del_flag = false
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
            <if test="statisticsDto.isReceive != null">
                LEFT JOIN proposal_reception pr ON p.id = pr.proposal_id AND pr.receive_status = #{statisticsDto.isReceive} AND pr.del_flag = false
            </if>
        WHERE p.del_flag = false
            AND p.year = #{statisticsDto.statisticalYear}
        <if test="statisticsDto.userIdList != null and statisticsDto.userIdList.size > 0">
            AND pu.unit_id IN
            <foreach collection="statisticsDto.userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="analyzeByGovOrPartyDept" resultType="com.ruoyi.project.proposal.domain.vo.UndertakeUnitVo">
        SELECT
            pu.id,
            pu.unit_id,
            u.user_name AS unit_name,
            pu.undertake_way
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND EXTRACT(year FROM ph.create_time) BETWEEN #{statisticsDto.statisticalYear} AND #{statisticsDto.statisticalYear}
        <if test="statisticsDto.userIdList != null and statisticsDto.userIdList.size > 0">
            AND pu.unit_id IN
            <foreach collection="statisticsDto.userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="analyzeHandleProgress" resultType="com.ruoyi.project.proposal.domain.vo.HandleProgressStatsVo">
        SELECT user_name AS dept_name
        FROM sys_user
        WHERE user_name IN ( SELECT dept_name FROM `sys_dept` WHERE FIND_IN_SET('103', ancestors) = 2 )
        <if test="statisticsDto.userIdList != null and statisticsDto.userIdList.size > 0">
            AND user_id IN
            <foreach collection="statisticsDto.userIdList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="analyzeProposalReception"
            resultType="com.ruoyi.project.proposal.domain.vo.ProposalReceptionStatisticsVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason,
            IF(pr.receive_status, '已接收', '未接收') AS reception,
            CASE
                WHEN pr.undertake_way = 'LEAD_OFFICE' THEN '[主]'
                WHEN pr.undertake_way = 'ASSISTANT_OFFICE' THEN '[协]'
                WHEN pr.undertake_way = 'SINGLE_OFFICE' THEN '[单]'
            END AS undertake_way
        FROM proposal p
            INNER JOIN proposal_reception pr ON p.id = pr.proposal_id AND pr.del_flag = false
            INNER JOIN sys_user u ON pr.recipient_id = u.user_id
        WHERE p.del_flag = false
            AND p.year = #{statisticsDto.statisticalYear}
        <if test="statisticsDto.isReceive != null">
            AND pr.receive_status = #{statisticsDto.isReceive}
        </if>
    </select>

    <select id="analyzeHandleCount" resultType="com.ruoyi.project.proposal.domain.vo.HandleStatisticsVo">
        SELECT
            ph.undertake_way AS label,
            COUNT(*) AS count
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
        WHERE p.del_flag = false
            AND p.year = #{statisticsDto.statisticalYear}
        GROUP BY ph.undertake_way
    </select>

    <select id="selectFinishProposalUnit" resultType="java.lang.String">
        SELECT DISTINCT u.user_name
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND u.user_name IS NOT NULL
            AND p.year = #{statisticsDto.statisticalYear}
            AND p.case_filing = 'FINISH'
    </select>

    <select id="selectProcessingProposalUnit" resultType="java.lang.String">
        SELECT DISTINCT u.user_name
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id
            LEFT JOIN proposal_reception pr ON p.id = pr.proposal_id
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND u.user_name IS NOT NULL
            AND p.year = #{statisticsDto.statisticalYear}
            AND pr.receive_status = true
    </select>

    <select id="selectNotStartedProposalUnit" resultType="java.lang.String">
        SELECT DISTINCT u.user_name
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id
            LEFT JOIN proposal_reception pr ON p.id = pr.proposal_id
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND u.user_name IS NOT NULL
            AND p.year = #{statisticsDto.statisticalYear}
            AND pr.receive_status = false
    </select>

    <select id="selectProposalHandle" resultType="com.ruoyi.project.proposal.domain.ProposalHandle">
        SELECT ph.*
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
        WHERE p.del_flag = false
            AND p.case_filing = 'FINISH'
            AND EXTRACT(year FROM ph.create_time) BETWEEN #{statisticsDto.statisticalYear} AND #{statisticsDto.statisticalYear}
    </select>

    <select id="selectHandleProgress" resultType="com.ruoyi.project.proposal.domain.vo.HandleStatisticsVo">
        SELECT
            u.user_name AS label,
            COUNT(DISTINCT p.id) AS count
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id AND pu.del_flag = false
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND p.`year` = #{statisticsDto.statisticalYear}
            AND u.user_name IS NOT NULL
            AND pu.undertake_way IS NOT NULL
            AND pu.undertake_way IN ('SINGLE_OFFICE', 'JOINT_HANDLING')
        GROUP BY u.user_name
    </select>

    <!-- 会办已报：承办方式为会办且承办单位已答复 -->
    <select id="selectJointOfficeReported" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id AND pu.del_flag = false
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
            INNER JOIN proposal_feedback pf ON pf.proposal_id = p.id AND pf.reply_user_id = pu.unit_id AND pf.del_flag = false
        WHERE p.del_flag = false
            AND p.`year` = #{year}
            AND pu.undertake_way = 'JOINT_HANDLING'
            AND u.user_name IS NOT NULL
    </select>

    <!-- 会办未报：承办方式为会办且承办单位已接收但未答复 -->
    <select id="selectJointOfficeUnReported" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id AND pu.del_flag = false
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
            LEFT JOIN proposal_reception pr ON pr.proposal_id = p.id AND pr.recipient_id = pu.unit_id AND pr.del_flag = false
            LEFT JOIN proposal_feedback pf ON pf.proposal_id = p.id AND pf.reply_user_id = pu.unit_id AND pf.del_flag = false
        WHERE p.del_flag = false
            AND p.`year` = #{year}
            AND pu.undertake_way = 'JOINT_HANDLING'
            AND u.user_name IS NOT NULL
            AND pr.receive_status = true
            AND pf.id IS NULL
    </select>

    <!-- 主办已报：承办方式为单办且承办单位已答复 -->
    <select id="selectLeadOfficeReported" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id AND pu.del_flag = false
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
            INNER JOIN proposal_feedback pf ON pf.proposal_id = p.id AND pf.reply_user_id = pu.unit_id AND pf.del_flag = false
        WHERE p.del_flag = false
            AND p.`year` = #{year}
            AND pu.undertake_way = 'SINGLE_OFFICE'
            AND u.user_name IS NOT NULL
    </select>

    <!-- 主办未报：承办方式为单办且承办单位已接收但未答复 -->
    <select id="selectLeadOfficeUnReported" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id AND pu.del_flag = false
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
            LEFT JOIN proposal_reception pr ON pr.proposal_id = p.id AND pr.recipient_id = pu.unit_id AND pr.del_flag = false
            LEFT JOIN proposal_feedback pf ON pf.proposal_id = p.id AND pf.reply_user_id = pu.unit_id AND pf.del_flag = false
        WHERE p.del_flag = false
            AND p.`year` = #{year}
            AND pu.undertake_way = 'SINGLE_OFFICE'
            AND u.user_name IS NOT NULL
            AND pr.receive_status = true
            AND pf.id IS NULL
    </select>

    <!-- 保留原有方法作为兼容，但标记为废弃 -->
    <select id="selectJointOffice" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND p.`year` = #{year}
            AND ph.undertake_way = 'JOINT_HANDLING'
            AND user_name is NOT NULL
    </select>

    <select id="selectLeadOffice" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUnitStatisticVo">
        SELECT
            u.user_name AS unit_name,
            LPAD(p.case_number, 4, '0') AS case_number
        FROM proposal p
            LEFT JOIN proposal_handle ph ON p.id = ph.proposal_id
            LEFT JOIN proposal_undertake_unit pu ON ph.id = pu.handle_id
            LEFT JOIN sys_user u ON pu.unit_id = u.user_id
        WHERE p.del_flag = false
            AND p.`year` = #{year}
            AND ph.undertake_way = 'JOINT_HANDLING'
            AND pu.undertake_way = 'LEAD_OFFICE'
            AND user_name is NOT NULL
    </select>

    <select id="analyzeMemberProposal" resultType="com.ruoyi.project.proposal.domain.vo.MemberProposalStatisticsVo">
        SELECT
            u.user_name AS member_name,
            SUM(CASE
                WHEN pur.submit_type = '领衔' OR pur.submit_type = '单独' THEN 1
                ELSE 0
            END) AS individual_count,
            SUM(CASE
                WHEN pur.submit_type = '附议' THEN 1
                ELSE 0
            END) AS motion_count,
            0 AS important_count,
            0 AS excellent_count
        FROM proposal_user_rel pur
            LEFT JOIN sys_user u ON pur.proposer_id = u.user_id
            LEFT JOIN proposal p ON pur.proposal_id = p.id
        WHERE pur.del_flag = 0
            AND p.del_flag = 0
            AND p.year = #{year}
            AND u.user_name IS NOT NULL
        GROUP BY u.user_id, u.user_name
        ORDER BY u.user_name
    </select>

    <select id="analyzeMemberProposalDetail" resultType="com.ruoyi.project.proposal.domain.vo.MemberProposalDetailStatisticsVo">
        SELECT
            u.user_name AS member_name,
            -- 立案统计 (包括PUT_ON、MERGED、WAIT_HANDLE、FINISH)
            SUM(CASE
                WHEN p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH') AND pur.submit_type = '单独' THEN 1
                ELSE 0
            END) AS filed_individual_count,
            SUM(CASE
                WHEN p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH') AND pur.submit_type = '领衔' THEN 1
                ELSE 0
            END) AS filed_leader_count,
            SUM(CASE
                WHEN p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH') AND pur.submit_type = '附议' THEN 1
                ELSE 0
            END) AS filed_motion_count,
            -- 不立案统计 (只有NOT_PUT_ON)
            SUM(CASE
                WHEN p.case_filing = 'NOT_PUT_ON' AND pur.submit_type = '单独' THEN 1
                ELSE 0
            END) AS not_filed_individual_count,
            SUM(CASE
                WHEN p.case_filing = 'NOT_PUT_ON' AND pur.submit_type = '领衔' THEN 1
                ELSE 0
            END) AS not_filed_leader_count,
            SUM(CASE
                WHEN p.case_filing = 'NOT_PUT_ON' AND pur.submit_type = '附议' THEN 1
                ELSE 0
            END) AS not_filed_motion_count
        FROM proposal_user_rel pur
            LEFT JOIN sys_user u ON pur.proposer_id = u.user_id
            LEFT JOIN proposal p ON pur.proposal_id = p.id
        WHERE pur.del_flag = 0
            AND p.del_flag = 0
            AND p.year = #{year}
            AND u.user_name IS NOT NULL
        GROUP BY u.user_id, u.user_name
        HAVING (filed_individual_count + filed_leader_count + filed_motion_count +
                not_filed_individual_count + not_filed_leader_count + not_filed_motion_count) > 0
        ORDER BY u.user_name
    </select>

</mapper>