package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;

import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.dto.ActivityJoinedPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityParticipantAbsenceDTO;
import com.ruoyi.project.activity.domain.dto.ActivityRegistrationDTO;
import com.ruoyi.project.activity.domain.dto.ActivityRegistrationPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityBasicinfoVO;
import com.ruoyi.project.activity.domain.vo.ActivityJoinedVO;
import com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO;
import com.ruoyi.project.activity.mapper.ActivityParticipantsMapper;
import com.ruoyi.project.activity.service.IActivityBasicinfoService;
import com.ruoyi.project.activity.service.IActivityParticipantsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 活动参与人员控制器
 */
@RestController
@RequestMapping("/activity/participants")
public class ActivityParticipantsController extends BaseController {

    @Autowired
    private IActivityParticipantsService activityParticipantsService;

    @Autowired
    private ActivityParticipantsMapper activityParticipantsMapper;

    @Autowired
    private IActivityBasicinfoService activityBasicinfoService;

    /**
     * 获取参与人员列表
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:list')")
    @GetMapping("/list")
    public TableDataInfo list(ActivityParticipants activityParticipants) {
        startPage();
        LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();

        if (activityParticipants.getActivityPkid() != null && !activityParticipants.getActivityPkid().isEmpty()) {
            queryWrapper.eq(ActivityParticipants::getActivityPkid, activityParticipants.getActivityPkid());
        }

        if (activityParticipants.getPepolePkid() != null && !activityParticipants.getPepolePkid().isEmpty()) {
            queryWrapper.eq(ActivityParticipants::getPepolePkid, activityParticipants.getPepolePkid());
        }

        if (activityParticipants.getIsAttend() != null && !activityParticipants.getIsAttend().isEmpty()) {
            queryWrapper.eq(ActivityParticipants::getIsAttend, activityParticipants.getIsAttend());
        }

        // 按排序字段升序排列
        queryWrapper.orderByAsc(ActivityParticipants::getSort);

        List<ActivityParticipants> list = activityParticipantsService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取参与人员详细信息
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(activityParticipantsService.getById(id));
    }

    /**
     * 根据活动ID查询当前登录用户参加活动的详细情况
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:query')")
    @GetMapping("/detail/{activityId}")
    public AjaxResult getParticipantDetail(@PathVariable String activityId) {
        // 获取当前登录用户名
        String userName = SecurityUtils.getUsername();

        ActivityParticipantsVO participantDetail = activityParticipantsService.getParticipantDetailByActivityAndUserName(activityId, userName);
        return success(participantDetail);
    }

    /**
     * 获取某个活动的所有参与人员（包含姓名和职务信息）
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:list')")
    @GetMapping("/activity/{activityPkid}")
    public AjaxResult getParticipantsByActivity(@PathVariable String activityPkid) {
        List<ActivityParticipantsVO> participants = activityParticipantsMapper.getParticipantsByActivityId(activityPkid);
        return success(participants);
    }

    /**
     * 新增参与人员
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:add')")
    @Log(title = "参与人员管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ActivityParticipants activityParticipants) {
        return toAjax(activityParticipantsService.save(activityParticipants));
    }

    /**
     * 修改参与人员
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:edit')")
    @Log(title = "参与人员管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ActivityParticipants activityParticipants) {
        return toAjax(activityParticipantsService.updateById(activityParticipants));
    }

    /**
     * 删除参与人员
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:remove')")
    @Log(title = "参与人员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(activityParticipantsService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 更新参与人员的参加状态（不参加/请假）
     *
     * isAttend: 1-参加, 0-不参加, 2-请假
     */
    @PreAuthorize("@ss.hasPermi('activity:participants:edit')")
    @Log(title = "参与人员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/absence")
    public AjaxResult updateAttendStatus(@RequestBody @Validated ActivityParticipantAbsenceDTO absenceDTO) {
        return toAjax(activityParticipantsService.updateParticipantAttendStatus(absenceDTO));
    }

    /**
     * 获取当前登录用户可以参加报名的活动分页列表
     */
    @GetMapping("/registration/page")
    public TableDataInfo getRegistrationList(ActivityRegistrationPageDTO pageDTO) {
        // 获取当前登录用户ID
        String userId = SecurityUtils.getUserId().toString();

        // 查询当前用户可以参加报名的活动列表
        IPage<ActivityBasicinfoVO> page = activityParticipantsService.getCurrentUserRegistrationList(userId, pageDTO);

        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 获取当前登录用户已参加的活动分页列表
     */
    @GetMapping("/joined/page")
    public TableDataInfo getJoinedActivityList(ActivityJoinedPageDTO pageDTO) {
        // 获取当前登录用户ID
        String userId = SecurityUtils.getUserId().toString();

        // 查询当前用户已参加的活动列表
        IPage<ActivityJoinedVO> page = activityParticipantsService.getCurrentUserJoinedActivityList(userId, pageDTO);

        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 用户报名参加活动
     */
    @Log(title = "活动报名", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public AjaxResult registerActivity(@RequestBody @Validated ActivityRegistrationDTO registrationDTO) {
        // 获取当前登录用户ID
        String userId = SecurityUtils.getUserId().toString();

        // 调用报名服务
        boolean result = activityParticipantsService.registerActivity(userId, registrationDTO);

        return toAjax(result);
    }
}