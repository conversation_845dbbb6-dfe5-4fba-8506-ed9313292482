package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.dto.ActivityBasicinfoDTO;
import com.ruoyi.project.activity.domain.dto.ActivityBasicinfoPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityStatusBatchDTO;
import com.ruoyi.project.activity.domain.vo.ActivityBasicinfoVO;
import com.ruoyi.project.activity.service.IActivityBasicinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动基本信息控制器
 */
@RestController
@RequestMapping("/activity/basicinfo")
public class ActivityBasicinfoController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ActivityBasicinfoController.class);

    @Autowired
    private IActivityBasicinfoService activityBasicinfoService;

    /**
     * 获取活动列表
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(ActivityBasicinfo activityBasicinfo) {
        try {
            startPage();
            List<ActivityBasicinfo> list = activityBasicinfoService.selectActivityBasicinfoList(activityBasicinfo);
            return getDataTable(list);
        } catch (RuntimeException e) {
            log.error("查询活动列表失败：", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 获取活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:query') OR hasRole('SUPER')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(activityBasicinfoService.selectActivityBasicinfoById(id));
    }

    /**
     * 新增活动
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:add')")
    @Log(title = "活动管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Validated ActivityBasicinfoDTO activityBasicinfoDTO) {
        try {
            return toAjax(activityBasicinfoService.insertActivityBasicinfo(activityBasicinfoDTO));
        } catch (RuntimeException e) {
            log.error("新增活动失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 修改活动
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:edit')")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated ActivityBasicinfoDTO activityBasicinfoDTO) {
        try {
            return toAjax(activityBasicinfoService.updateActivityBasicinfo(activityBasicinfoDTO));
        } catch (RuntimeException e) {
            log.error("修改活动失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 删除活动
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:remove')")
    @Log(title = "活动管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        try {
            return toAjax(activityBasicinfoService.deleteActivityBasicinfoByIds(ids));
        } catch (RuntimeException e) {
            log.error("删除活动失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 获取活动参与人员
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:query')")
    @GetMapping("/participants/{activityId}")
    public AjaxResult getParticipants(@PathVariable String activityId) {
        try {
            List<String> participants = activityBasicinfoService.getActivityParticipants(activityId);
            return success(participants);
        } catch (RuntimeException e) {
            log.error("获取活动参与人员失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 分页查询活动列表（POST方式）
     */
    //@PreAuthorize("@ss.hasPermi('activity:basicinfo:list')")
    @PostMapping("/page")
    public TableDataInfo pagePost(@RequestBody ActivityBasicinfoPageDTO pageDTO) {
        try {
            IPage<ActivityBasicinfoVO> page = activityBasicinfoService.pageActivityBasicinfo(pageDTO);
            return getDataTable(page.getRecords(), page.getTotal());
        } catch (RuntimeException e) {
            log.error("分页查询活动列表失败：", e);
            return getDataTable(new ArrayList<>(), 0L);
        }
    }

    /**
     * 批量修改活动发布状态
     */
    @PreAuthorize("@ss.hasPermi('activity:basicinfo:edit')")
    @Log(title = "活动管理", businessType = BusinessType.UPDATE)
    @PutMapping("/batchStatus")
    public AjaxResult batchUpdateStatus(@RequestBody @Validated ActivityStatusBatchDTO statusDTO) {
        try {
            int rows = activityBasicinfoService.updateActivityStatusBatch(statusDTO.getIds(), statusDTO.getStatus());
            return toAjax(rows);
        } catch (RuntimeException e) {
            log.error("批量修改活动状态失败：", e);
            return error(e.getMessage());
        }
    }

}