package com.ruoyi.project.activity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动参与人员数据层
 */
@Mapper
public interface ActivityParticipantsMapper extends BaseMapper<ActivityParticipants> {

    /**
     * 获取活动参与人员列表（包含姓名和职务信息）
     *
     * @param activityPkid 活动ID
     * @return 活动参与人员列表
     */
    List<ActivityParticipantsVO> getParticipantsByActivityId(@Param("activityPkid") String activityPkid);

    /**
     * 根据活动ID和人员ID查询该人员参加活动的详细情况（包含姓名和职务信息）
     *
     * @param activityPkid 活动ID
     * @param pepolePkid   人员ID
     * @return 参与详情
     */
    ActivityParticipantsVO getParticipantDetailByActivityAndPeople(@Param("activityPkid") String activityPkid,
            @Param("pepolePkid") String pepolePkid);

    /**
     * 根据活动ID和用户名查询该用户参加活动的详细情况
     * 注意：只要activity_participants表中存在记录，就算用户参加了该活动
     *
     * @param activityPkid 活动ID
     * @param userName     用户名
     * @return 参与详情
     */
    ActivityParticipantsVO getParticipantDetailByActivityAndUserName(@Param("activityPkid") String activityPkid,
            @Param("userName") String userName);

    /**
     * 根据当前用户userId和活动ID获取用户在该活动中的委员id列表（前端设计问题，选择委员时没有过滤掉userID重复的数据，所以可以出现同一个用户在该活动中出现多条数据）
     * 
     * @param activityPkid 活动ID
     * @param userId       当前登录用户ID
     * @return 用户在该活动中的委员id列表
     */
    List<String> getCommitteeIdsByActivityAndUser(@Param("activityPkid") String activityPkid,
            @Param("userId") String userId);
}