package com.ruoyi.project.activity.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户活动签到明细VO
 */
@Data
@ApiModel(value = "用户活动签到明细VO")
public class UserActivitySignDetailVO {

    /**
     * UUID主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 活动标题
     */
    @ApiModelProperty(value = "活动标题")
    private String title;

    /**
     * 签到开始时间
     */
    @ApiModelProperty(value = "签到开始时间")
    private Date signBegindate;

    /**
     * 签到结束时间
     */
    @ApiModelProperty(value = "签到结束时间")
    private Date signEnddate;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "签到时间")
    private Date beginDate;

    /** 签到时间 */
    @ApiModelProperty(value = "签到时间")
    private Date signDate;

    /**
     * 签到说明
     */
    @ApiModelProperty(value = "签到说明")
    private String signDesc;

    /**
     * 签到id
     */
    @ApiModelProperty(value = "签到id")
    private String signId;

    /**
     * 签到pkid
     */
    @ApiModelProperty(value = "签到pkid")
    private String signPkid;

    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String pepoleName;

    /**
     * 签到发起人
     */
    @ApiModelProperty(value = "签到发起人")
    private String sponsor;

    /**
     * 是否签到(1:是 0:否)
     */
    @ApiModelProperty(value = "是否签到(1:是 0:否)")
    private String isSign;

    /**
     * 签到类型
     */
    @ApiModelProperty(value = "签到类型")
    private String signType;

    /**
     * 是否请假(1:是 0:否)
     */
    @ApiModelProperty(value = "是否请假(1:是 0:否)")
    private String isLeave;

    /**
     * 原因说明
     */
    @ApiModelProperty(value = "原因说明")
    private String reason;

    /**
     * 请假原因
     */
    @ApiModelProperty(value = "请假原因")
    private String leaveReason;

}