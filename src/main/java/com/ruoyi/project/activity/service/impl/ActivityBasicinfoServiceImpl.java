package com.ruoyi.project.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.dto.ActivityBasicinfoDTO;
import com.ruoyi.project.activity.domain.dto.ActivityBasicinfoPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityBasicinfoVO;
import com.ruoyi.project.activity.mapper.ActivityBasicinfoMapper;
import com.ruoyi.project.activity.service.IActivityBasicinfoService;
import com.ruoyi.project.activity.service.IActivityParticipantsService;
import com.ruoyi.project.system.domain.SysDept;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.service.ISysDeptService;
import com.ruoyi.project.system.service.ISysUserService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动基本信息服务实现
 */
@Service
public class ActivityBasicinfoServiceImpl extends ServiceImpl<ActivityBasicinfoMapper, ActivityBasicinfo> implements IActivityBasicinfoService {

    @Autowired
    private IActivityParticipantsService activityParticipantsService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询活动基本信息列表
     *
     * @param activityBasicinfo 活动基本信息
     * @return 活动基本信息集合
     */
    @Override
    public List<ActivityBasicinfo> selectActivityBasicinfoList(ActivityBasicinfo activityBasicinfo) {
        LambdaQueryWrapper<ActivityBasicinfo> queryWrapper = new LambdaQueryWrapper<>();

        // 根据条件构建查询
        if (activityBasicinfo != null) {
            // 根据标题模糊查询
            if (StringUtils.isNotEmpty(activityBasicinfo.getTitle())) {
                queryWrapper.like(ActivityBasicinfo::getTitle, activityBasicinfo.getTitle());
            }

            // 根据活动类型精确查询
            if (StringUtils.isNotEmpty(activityBasicinfo.getType())) {
                queryWrapper.eq(ActivityBasicinfo::getType, activityBasicinfo.getType());
            }

            // 根据状态精确查询
            if (StringUtils.isNotEmpty(activityBasicinfo.getStatus())) {
                queryWrapper.eq(ActivityBasicinfo::getStatus, activityBasicinfo.getStatus());
            }

            // 根据活动城市查询
            if (StringUtils.isNotEmpty(activityBasicinfo.getActivityCity())) {
                queryWrapper.eq(ActivityBasicinfo::getActivityCity, activityBasicinfo.getActivityCity());
            }

            // 根据时间范围查询
            if (activityBasicinfo.getActivityBeginDate() != null) {
                queryWrapper.ge(ActivityBasicinfo::getActivityBeginDate, activityBasicinfo.getActivityBeginDate());
            }

            if (activityBasicinfo.getActivityEndDate() != null) {
                queryWrapper.le(ActivityBasicinfo::getActivityEndDate, activityBasicinfo.getActivityEndDate());
            }
        }

        // 默认按创建时间降序排序
        queryWrapper.orderByDesc(ActivityBasicinfo::getCreateTime);

        return list(queryWrapper);
    }

    /**
     * 查询活动基本信息详细
     *
     * @param id 活动基本信息ID
     * @return 活动基本信息
     */
    @Override
    public ActivityBasicinfo selectActivityBasicinfoById(Long id) {
        return getById(id);
    }

    /**
     * 新增活动基本信息
     *
     * @param activityBasicinfoDTO 活动基本信息DTO
     * @return 结果
     */
    @Override
    @Transactional
    public int insertActivityBasicinfo(ActivityBasicinfoDTO activityBasicinfoDTO) {
        // 验证时间逻辑
        if (activityBasicinfoDTO.getActivityBeginDate() != null && activityBasicinfoDTO.getActivityEndDate() != null) {
            if (activityBasicinfoDTO.getActivityBeginDate().after(activityBasicinfoDTO.getActivityEndDate())) {
                throw new RuntimeException("活动开始时间不能晚于结束时间");
            }
        }

        // 如果有报名时间，也需要验证
        if (activityBasicinfoDTO.getEnterBeginDate() != null && activityBasicinfoDTO.getEnterEndDate() != null) {
            if (activityBasicinfoDTO.getEnterBeginDate().after(activityBasicinfoDTO.getEnterEndDate())) {
                throw new RuntimeException("报名开始时间不能晚于报名结束时间");
            }
        }

        // 转换DTO为实体
        ActivityBasicinfo activityBasicinfo = new ActivityBasicinfo();
        BeanUtils.copyProperties(activityBasicinfoDTO, activityBasicinfo);

        // 设置创建者信息
        activityBasicinfo.setCreateId(SecurityUtils.getUsername());
        activityBasicinfo.setCreateTime(new Date());

        // 如果没有设置状态，默认设置为未发布状态
        if (StringUtils.isEmpty(activityBasicinfo.getStatus())) {
            activityBasicinfo.setStatus("0"); // 假设0表示未发布状态
        }

        // 如果没有设置是否存在，默认为存在
        if (StringUtils.isEmpty(activityBasicinfo.getIsExist())) {
            activityBasicinfo.setIsExist("1"); // 1表示存在
        }

        // 生成PKID（如果需要）
        if (StringUtils.isEmpty(activityBasicinfo.getPkid())) {
            activityBasicinfo.setPkid(java.util.UUID.randomUUID().toString().replace("-", ""));
        }

        boolean saved = save(activityBasicinfo);

        // 保存参与人员
        if (saved && activityBasicinfoDTO.getParticipantIds() != null && !activityBasicinfoDTO.getParticipantIds().isEmpty()) {
            saveActivityParticipants(activityBasicinfo.getId(), activityBasicinfoDTO.getParticipantIds());
        }

        return saved ? 1 : 0;
    }

    /**
     * 修改活动基本信息
     *
     * @param activityBasicinfoDTO 活动基本信息DTO
     * @return 结果
     */
    @Override
    @Transactional
    public int updateActivityBasicinfo(ActivityBasicinfoDTO activityBasicinfoDTO) {
        // 验证必填字段
        if (activityBasicinfoDTO.getId() == null) {
            throw new RuntimeException("活动ID不能为空");
        }

        // 验证时间逻辑
        if (activityBasicinfoDTO.getActivityBeginDate() != null && activityBasicinfoDTO.getActivityEndDate() != null) {
            if (activityBasicinfoDTO.getActivityBeginDate().after(activityBasicinfoDTO.getActivityEndDate())) {
                throw new RuntimeException("活动开始时间不能晚于结束时间");
            }
        }

        // 如果有报名时间，也需要验证
        if (activityBasicinfoDTO.getEnterBeginDate() != null && activityBasicinfoDTO.getEnterEndDate() != null) {
            if (activityBasicinfoDTO.getEnterBeginDate().after(activityBasicinfoDTO.getEnterEndDate())) {
                throw new RuntimeException("报名开始时间不能晚于报名结束时间");
            }
        }

        // 获取原有记录
        ActivityBasicinfo existingActivity = getById(activityBasicinfoDTO.getId());
        if (existingActivity == null) {
            throw new RuntimeException("未找到要修改的活动信息");
        }

        // 转换DTO为实体
        ActivityBasicinfo activityBasicinfo = new ActivityBasicinfo();
        BeanUtils.copyProperties(activityBasicinfoDTO, activityBasicinfo);

        // 设置更新者信息
        activityBasicinfo.setUpdateId(SecurityUtils.getUsername());
        activityBasicinfo.setUpdateTime(new Date());

        boolean updated = updateById(activityBasicinfo);

        // 更新参与人员
        if (updated && activityBasicinfoDTO.getParticipantIds() != null) {
            // 先删除原有参与人员
            LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ActivityParticipants::getActivityPkid, existingActivity.getId());
            activityParticipantsService.remove(queryWrapper);

            // 再添加新的参与人员
            if (!activityBasicinfoDTO.getParticipantIds().isEmpty()) {
                saveActivityParticipants(existingActivity.getId(), activityBasicinfoDTO.getParticipantIds());
            }
        }

        return updated ? 1 : 0;
    }

    /**
     * 批量删除活动基本信息
     *
     * @param ids 需要删除的活动基本信息ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteActivityBasicinfoByIds(Long[] ids) {
        // 获取要删除的活动信息
        List<ActivityBasicinfo> activities = listByIds(Arrays.asList(ids));
        if (activities == null || activities.isEmpty()) {
            return 0;
        }

        // 删除活动参与人员
        for (ActivityBasicinfo activity : activities) {
            if (activity.getPkid() != null) {
                LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ActivityParticipants::getActivityPkid, activity.getPkid());
                activityParticipantsService.remove(queryWrapper);
            }
        }

        // 删除活动基本信息
        return removeByIds(Arrays.asList(ids)) ? ids.length : 0;
    }

    /**
     * 删除活动基本信息信息
     *
     * @param id 活动基本信息ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteActivityBasicinfoById(Long id) {
        // 获取要删除的活动信息
        ActivityBasicinfo activity = getById(id);
        if (activity == null) {
            return 0;
        }

        // 删除活动参与人员
        if (activity.getPkid() != null) {
            LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ActivityParticipants::getActivityPkid, activity.getPkid());
            activityParticipantsService.remove(queryWrapper);
        }

        // 删除活动基本信息
        return removeById(id) ? 1 : 0;
    }

    /**
     * 获取活动参与人员列表
     *
     * @param activityId 活动ID
     * @return 参与人员ID列表
     */
    @Override
    public List<String> getActivityParticipants(String activityId) {
        LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityParticipants::getActivityPkid, activityId);
        List<ActivityParticipants> participants = activityParticipantsService.list(queryWrapper);

        if (participants == null || participants.isEmpty()) {
            return new ArrayList<>();
        }

        return participants.stream()
                .map(ActivityParticipants::getPepolePkid)
                .collect(Collectors.toList());
    }

    /**
     * 保存活动参与人员
     *
     * @param activityId 活动ID
     * @param participantIds 参与人员ID列表
     * @return 结果
     */
    public boolean saveActivityParticipants(Long activityId, List<String> participantIds) {
        if (activityId == null || participantIds == null || participantIds.isEmpty()) {
            return false;
        }

        List<ActivityParticipants> participantsList = new ArrayList<>();

        for (String participantId : participantIds) {
            ActivityParticipants participant = new ActivityParticipants();
            participant.setPkid(java.util.UUID.randomUUID().toString().replace("-", ""));
            participant.setActivityPkid(activityId.toString());
            participant.setPepolePkid(participantId);
            participant.setIsAttend("1"); // 默认参加
            participant.setCreateId(SecurityUtils.getUsername());
            participant.setCreateTime(new Date());
            participantsList.add(participant);
        }

        return activityParticipantsService.saveBatch(participantsList);
    }

    /**
     * 分页查询活动列表
     *
     * @param pageDTO 分页查询参数
     * @return 分页结果
     */
    @Override
    public IPage<ActivityBasicinfoVO> pageActivityBasicinfo(ActivityBasicinfoPageDTO pageDTO) {
        // 创建分页对象
        Page<ActivityBasicinfo> page = new Page<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<ActivityBasicinfo> queryWrapper = new LambdaQueryWrapper<>();

        // 活动主题模糊查询
        if (StringUtils.isNotEmpty(pageDTO.getTitle())) {
            queryWrapper.like(ActivityBasicinfo::getTitle, pageDTO.getTitle());
        }

        // 活动类型精确查询
        if (StringUtils.isNotEmpty(pageDTO.getType())) {
            queryWrapper.eq(ActivityBasicinfo::getType, pageDTO.getType());
        }

        // 活动城市精确查询
        if (StringUtils.isNotEmpty(pageDTO.getActivityCity())) {
            queryWrapper.eq(ActivityBasicinfo::getActivityCity, pageDTO.getActivityCity());
        }

        // 活动状态精确查询
        if (StringUtils.isNotEmpty(pageDTO.getStatus())) {
            queryWrapper.eq(ActivityBasicinfo::getStatus, pageDTO.getStatus());
        }

        // 活动开始时间范围查询
        if (pageDTO.getBeginTime() != null) {
            queryWrapper.ge(ActivityBasicinfo::getActivityBeginDate, pageDTO.getBeginTime());
        }

        if (pageDTO.getEndTime() != null) {
            queryWrapper.le(ActivityBasicinfo::getActivityBeginDate, pageDTO.getEndTime());
        }

        // 补录状态精确查询
        if (StringUtils.isNotEmpty(pageDTO.getSupplementStatus())) {
            queryWrapper.eq(ActivityBasicinfo::getSupplementStatus, pageDTO.getSupplementStatus());
        }

        // 发起部门名称模糊查询
        if (StringUtils.isNotEmpty(pageDTO.getDeptName())) {
            // 查询符合部门名称条件的用户名列表
            List<String> userNames = getUserNamesByDeptName(pageDTO.getDeptName());
            if (!userNames.isEmpty()) {
                queryWrapper.in(ActivityBasicinfo::getCreateId, userNames);
            } else {
                // 如果没有找到匹配的部门，返回空结果
                return new Page<>();
            }
        }

        // 默认按创建时间降序排序
        queryWrapper.orderByDesc(ActivityBasicinfo::getCreateTime);

        // 执行分页查询
        IPage<ActivityBasicinfo> resultPage = page(page, queryWrapper);

        // 转换为VO对象
        IPage<ActivityBasicinfoVO> voPage = resultPage.convert(activity -> {
            ActivityBasicinfoVO vo = new ActivityBasicinfoVO();
            BeanUtils.copyProperties(activity, vo);

            // 查询参与人数
            try {
                LambdaQueryWrapper<ActivityParticipants> participantsQuery = new LambdaQueryWrapper<>();
                participantsQuery.eq(ActivityParticipants::getActivityPkid, activity.getPkid());
                long count = activityParticipantsService.count(participantsQuery);
                vo.setParticipantCount((int) count);
            } catch (Exception e) {
                // 如果查询参与人数失败，设置为0
                vo.setParticipantCount(0);
            }

            // 查询发起部门信息
            try {
                if (StringUtils.isNotEmpty(activity.getCreateId())) {
                    // 通过创建者用户名查询用户信息
                    SysUser user = userService.selectUserByUserName(activity.getCreateId());
                    if (user != null && user.getDeptId() != null) {
                        // 设置部门ID
                        vo.setDeptId(user.getDeptId());

                        // 查询部门信息
                        SysDept dept = deptService.selectDeptById(user.getDeptId());
                        if (dept != null) {
                            // 设置部门名称
                            vo.setDeptName(dept.getDeptName());
                        }
                    }
                }
            } catch (Exception e) {
                // 如果查询部门信息失败，不设置部门信息
                vo.setDeptId(null);
                vo.setDeptName(null);
            }

            return vo;
        });

        return voPage;
    }

    /**
     * 根据部门名称模糊查询获取用户名列表
     *
     * @param deptName 部门名称
     * @return 用户名列表
     */
    private List<String> getUserNamesByDeptName(String deptName) {
        // 1. 查询匹配部门名称的部门ID列表
        SysDept queryDept = new SysDept();
        queryDept.setDeptName(deptName);
        List<SysDept> depts = deptService.selectDeptList(queryDept);

        if (depts == null || depts.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 提取部门ID列表
        List<Long> deptIds = depts.stream()
                .map(SysDept::getDeptId)
                .collect(Collectors.toList());

        // 3. 查询这些部门下的用户
        List<String> userNames = new ArrayList<>();
        for (Long deptId : deptIds) {
            SysUser queryUser = new SysUser();
            queryUser.setDeptId(deptId);
            List<SysUser> users = userService.selectUserList(queryUser);
            if (users != null && !users.isEmpty()) {
                List<String> names = users.stream()
                        .map(SysUser::getUserName)
                        .collect(Collectors.toList());
                userNames.addAll(names);
            }
        }

        return userNames;
    }

    /**
     * 批量修改活动发布状态
     *
     * @param ids 活动ID列表
     * @param status 发布状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateActivityStatusBatch(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty() || StringUtils.isEmpty(status)) {
            return 0;
        }

        // 获取要修改的活动列表
        List<ActivityBasicinfo> activities = listByIds(ids);
        if (activities == null || activities.isEmpty()) {
            return 0;
        }

        // 批量更新状态
        for (ActivityBasicinfo activity : activities) {
            activity.setStatus(status);
            activity.setUpdateId(SecurityUtils.getUsername());
            activity.setUpdateTime(new Date());
        }

        // 保存更新
        boolean result = updateBatchById(activities);
        return result ? activities.size() : 0;
    }
}