package com.ruoyi.project.activity.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.ActivitySign;
import com.ruoyi.project.activity.domain.ActivitySignDetail;
import com.ruoyi.project.activity.domain.dto.ActivityJoinedPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityParticipantAbsenceDTO;
import com.ruoyi.project.activity.domain.dto.ActivityRegistrationDTO;
import com.ruoyi.project.activity.domain.dto.ActivityRegistrationPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityBasicinfoVO;
import com.ruoyi.project.activity.domain.vo.ActivityJoinedVO;
import com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO;
import com.ruoyi.project.activity.mapper.ActivityBasicinfoMapper;
import com.ruoyi.project.activity.mapper.ActivityParticipantsMapper;
import com.ruoyi.project.activity.mapper.ActivitySignDetailMapper;
import com.ruoyi.project.activity.mapper.ActivitySignMapper;
import com.ruoyi.project.system.domain.SysDept;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.mapper.SysDeptMapper;
import com.ruoyi.project.system.mapper.SysUserMapper;
import com.ruoyi.project.activity.service.IActivityParticipantsService;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活动参与人员服务实现
 */
@Service
public class ActivityParticipantsServiceImpl extends ServiceImpl<ActivityParticipantsMapper, ActivityParticipants>
        implements IActivityParticipantsService {

    @Autowired
    private ActivityBasicinfoMapper activityBasicinfoMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ActivitySignMapper activitySignMapper;

    @Autowired
    private ActivitySignDetailMapper activitySignDetailMapper;

    @Autowired
    private CommitteeMemberMapper committeeMemberMapper;

    /**
     * 更新参与人员的参加状态和不参加/请假原因
     *
     * @param absenceDTO 不参加/请假DTO
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateParticipantAttendStatus(ActivityParticipantAbsenceDTO absenceDTO) {
        ActivityBasicinfo activity = activityBasicinfoMapper.selectById(absenceDTO.getActivityPkid());
        if (activity == null) {
            throw new ServiceException("活动不存在");
        }

        // 构建更新条件
        for (String peoplePkid : absenceDTO.getPeoplePkids()) {
            LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ActivityParticipants::getPepolePkid, peoplePkid);
            queryWrapper.eq(ActivityParticipants::getActivityPkid, absenceDTO.getActivityPkid());
            ActivityParticipants participant = this.getOne(queryWrapper);
            if (participant != null) {
                // 构建更新内容
                participant.setIsAttend(absenceDTO.getIsAttend());
                if (absenceDTO.getIsAttend().equals("2") || absenceDTO.getIsAttend().equals("0")) {
                    if (StringUtils.isBlank(absenceDTO.getNoAttendReason())) {
                        throw new RuntimeException("请假原因不能为空");
                    }
                    participant.setNoAttendReason(absenceDTO.getNoAttendReason());
                }

                participant.setUpdateId(SecurityUtils.getUsername());
                participant.setUpdateTime(new Date());
                // 执行更新
                this.updateById(participant);
            }
        }


        // 查询该活动是否创建相关的签到信息（可能有多个签到记录）
        List<ActivitySign> activitySignList = activitySignMapper
                .selectList(new LambdaQueryWrapper<ActivitySign>().eq(ActivitySign::getActivityPkid, activity.getId()));

        if (activitySignList != null && !activitySignList.isEmpty()) {
            // 遍历所有签到记录
            for (ActivitySign activitySign : activitySignList) {
                // 查询该签到记录是否创建相关的签到明细信息
                List<ActivitySignDetail> activitySignDetailList = activitySignDetailMapper
                        .selectList(new LambdaQueryWrapper<ActivitySignDetail>().eq(ActivitySignDetail::getSignPkid,
                                activitySign.getId()));

                if (activitySignDetailList != null && !activitySignDetailList.isEmpty()
                        && (absenceDTO.getIsAttend().equals("0") || absenceDTO.getIsAttend().equals("2"))) {
                    // 如果用户选择不参加或请假，删除该用户的所有签到明细信息
                    for (ActivitySignDetail detail : activitySignDetailList) {
                        if (absenceDTO.getPeoplePkids().contains(detail.getPepolePkid())) {
                            activitySignDetailMapper.deleteById(detail.getId());
                        }
                    }
                }

                if (absenceDTO.getIsAttend().equals("1")) {
                    // 如果用户选择参加，检查是否需要创建签到明细信息
                    for (String peopleId : absenceDTO.getPeoplePkids()) {
                        // 检查该用户是否已有签到明细记录
                        List<ActivitySignDetail> existingDetails = activitySignDetailMapper.selectList(
                                new LambdaQueryWrapper<ActivitySignDetail>()
                                        .eq(ActivitySignDetail::getSignPkid, activitySign.getId().toString())
                                        .eq(ActivitySignDetail::getPepolePkid, peopleId));

                        // 如果没有签到明细记录，则创建
                        if (existingDetails == null || existingDetails.isEmpty()) {
                            ActivitySignDetail detail = new ActivitySignDetail();
                            detail.setPkid(UUID.randomUUID().toString());
                            detail.setSignPkid(activitySign.getId().toString());
                            detail.setPepolePkid(peopleId);
                            detail.setActivityPkid(activity.getId().toString());
                            detail.setBeginDate(activitySign.getSignBeginDate());
                            detail.setIsSign("0"); // 默认未签到
                            activitySignDetailMapper.insert(detail);
                        }
                    }
                }
            }
        }

        return true;

    }

    /**
     * 获取当前登录用户可以参加报名的活动分页列表
     * 查询当前用户没有参加过的活动
     *
     * @param userId  用户ID
     * @param pageDTO 分页查询参数
     * @return 活动分页列表
     */
    @Override
    public IPage<ActivityBasicinfoVO> getCurrentUserRegistrationList(String userId,
            ActivityRegistrationPageDTO pageDTO) {
        // 创建分页对象
        Page<ActivityBasicinfo> page = new Page<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());

        List<CommitteeMember> committeeMember = committeeMemberMapper.selectList(new LambdaQueryWrapper<CommitteeMember>()
                .eq(CommitteeMember::getUserId, userId)
                .orderByDesc(CommitteeMember::getYear));
        if (committeeMember == null) {
            throw new ServiceException("您不是委员会成员，无法查看可以报名的活动");
        }

        // 1. 查询当前用户已参加的活动ID列表
        LambdaQueryWrapper<ActivityParticipants> participantsQuery = new LambdaQueryWrapper<>();
        participantsQuery.eq(ActivityParticipants::getPepolePkid, committeeMember.get(0).getId().toString());
        List<ActivityParticipants> userParticipants = this.list(participantsQuery);

        // 提取用户已参加的活动ID列表
        List<String> participatedActivityIds = new ArrayList<>();
        if (userParticipants != null && !userParticipants.isEmpty()) {
            participatedActivityIds = userParticipants.stream()
                    .map(ActivityParticipants::getActivityPkid)
                    .collect(Collectors.toList());

            // 打印日志，查看获取到的活动ID列表
            System.out.println("用户已参加的活动ID列表: " + participatedActivityIds);
        }

        // 构建查询条件
        LambdaQueryWrapper<ActivityBasicinfo> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询已发布的活动
        queryWrapper.eq(ActivityBasicinfo::getStatus, "1"); // 1表示已发布状态

        // 排除用户已参加的活动
        if (!participatedActivityIds.isEmpty()) {
            // 打印日志，查看排除条件
            System.out.println("排除用户已参加的活动，条件: ActivityBasicinfo::getPkid notIn " + participatedActivityIds);
            queryWrapper.notIn(ActivityBasicinfo::getId, participatedActivityIds);
        }

        // 当前时间
        Date now = new Date();

        // 只查询报名时间范围内的活动（报名开始时间<=当前时间<=报名结束时间）
        queryWrapper.le(ActivityBasicinfo::getActivityBeginDate, now);
        queryWrapper.ge(ActivityBasicinfo::getActivityEndDate, now);

        // 活动主题模糊查询
        if (StringUtils.isNotEmpty(pageDTO.getTitle())) {
            queryWrapper.like(ActivityBasicinfo::getTitle, pageDTO.getTitle());
        }

        // 活动类型精确查询
        if (StringUtils.isNotEmpty(pageDTO.getType())) {
            queryWrapper.eq(ActivityBasicinfo::getType, pageDTO.getType());
        }

        // 活动城市精确查询
        if (StringUtils.isNotEmpty(pageDTO.getActivityCity())) {
            queryWrapper.eq(ActivityBasicinfo::getActivityCity, pageDTO.getActivityCity());
        }

        // 开始时间范围查询
        if (StringUtils.isNotEmpty(pageDTO.getBeginDateStart())) {
            queryWrapper.ge(ActivityBasicinfo::getActivityBeginDate, pageDTO.getBeginDateStart());
        }

        if (StringUtils.isNotEmpty(pageDTO.getBeginDateEnd())) {
            queryWrapper.le(ActivityBasicinfo::getActivityBeginDate, pageDTO.getBeginDateEnd());
        }

        // 默认按创建时间降序排序
        queryWrapper.orderByDesc(ActivityBasicinfo::getCreateTime);

        // 执行分页查询
        IPage<ActivityBasicinfo> resultPage = activityBasicinfoMapper.selectPage(page, queryWrapper);

        // 转换为VO对象
        IPage<ActivityBasicinfoVO> voPage = resultPage.convert(activity -> {
            ActivityBasicinfoVO vo = new ActivityBasicinfoVO();
            BeanUtils.copyProperties(activity, vo);
            return vo;
        });

        return voPage;
    }

    /**
     * 获取当前登录用户已参加的活动分页列表
     *
     * @param userId  用户ID
     * @param pageDTO 分页查询参数
     * @return 活动分页列表
     */
    @Override
    public IPage<ActivityJoinedVO> getCurrentUserJoinedActivityList(String userId, ActivityJoinedPageDTO pageDTO) {
        // 创建分页对象
        Page<ActivityBasicinfo> page = new Page<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());

        List<CommitteeMember> committeeMember = committeeMemberMapper.selectList(new LambdaQueryWrapper<CommitteeMember>()
                .eq(CommitteeMember::getUserId, userId)
                .orderByDesc(CommitteeMember::getYear));
        if (ObjectUtil.isEmpty(committeeMember)) {
            throw new ServiceException("您不是委员会成员，无法查看已参加的活动");
        }

        List<String> collect = committeeMember.stream().map(member -> member.getId().toString()).collect(Collectors.toList());

        // 1. 查询当前用户已参加的活动ID列表
        LambdaQueryWrapper<ActivityParticipants> participantsQuery = new LambdaQueryWrapper<>();
        participantsQuery.in(ActivityParticipants::getPepolePkid, collect);
        participantsQuery.orderByDesc(ActivityParticipants::getCreateTime);

        List<ActivityParticipants> userParticipants = this.list(participantsQuery);

        // 如果用户没有参加任何活动，返回空结果
        if (userParticipants == null || userParticipants.isEmpty()) {
            return new Page<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());
        }

        // 创建一个Map，用于存储活动ID和参与记录的映射关系
        Map<String, ActivityParticipants> activityParticipantsMap = userParticipants.stream()
                .collect(Collectors.toMap(
                        ActivityParticipants::getActivityPkid,
                        participant -> participant,
                        (existing, replacement) -> existing));

        // 提取用户已参加的活动ID列表
        List<String> participatedActivityIds = userParticipants.stream()
                .map(ActivityParticipants::getActivityPkid)
                .collect(Collectors.toList());

        // 打印日志，查看获取到的活动ID列表
        System.out.println("用户已参加的活动ID列表: " + participatedActivityIds);

        // 构建查询条件
        LambdaQueryWrapper<ActivityBasicinfo> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询已发布的活动
        queryWrapper.eq(ActivityBasicinfo::getStatus, "1"); // 1表示已发布状态

        // 只查询用户已参加的活动
        queryWrapper.in(ActivityBasicinfo::getId, participatedActivityIds);

        // 活动主题模糊查询
        if (StringUtils.isNotEmpty(pageDTO.getTitle())) {
            queryWrapper.like(ActivityBasicinfo::getTitle, pageDTO.getTitle());
        }

        // 活动类型精确查询
        if (StringUtils.isNotEmpty(pageDTO.getType())) {
            queryWrapper.eq(ActivityBasicinfo::getType, pageDTO.getType());
        }

        // 开始时间范围查询
        if (StringUtils.isNotEmpty(pageDTO.getBeginDateStart())) {
            queryWrapper.ge(ActivityBasicinfo::getActivityBeginDate, pageDTO.getBeginDateStart());
        }

        if (StringUtils.isNotEmpty(pageDTO.getBeginDateEnd())) {
            queryWrapper.le(ActivityBasicinfo::getActivityBeginDate, pageDTO.getBeginDateEnd());
        }

        // 默认按创建时间降序排序
        queryWrapper.orderByDesc(ActivityBasicinfo::getCreateTime);

        // 执行分页查询
        IPage<ActivityBasicinfo> resultPage = activityBasicinfoMapper.selectPage(page, queryWrapper);

        // 创建一个Map，用于存储用户ID和部门的映射关系
        final Map<String, SysDept> userDeptMap = new HashMap<>();

        // 转换为VO对象
        IPage<ActivityJoinedVO> voPage = resultPage.convert(activity -> {
            ActivityJoinedVO vo = new ActivityJoinedVO();
            BeanUtils.copyProperties(activity, vo);

            // 查询创建人的部门信息
            if (StringUtils.isNotEmpty(activity.getCreateId())) {
                try {
                    // 先从缓存中查找
                    SysDept dept = userDeptMap.get(activity.getCreateId());
                    if (dept == null) {
                        // 缓存中没有，则查询数据库
                        SysUser user = sysUserMapper.selectUserByUserName(activity.getCreateId());
                        if (user != null && user.getDeptId() != null) {
                            dept = sysDeptMapper.selectDeptById(user.getDeptId());
                            if (dept != null) {
                                // 放入缓存
                                userDeptMap.put(activity.getCreateId(), dept);
                            }
                        }
                    }

                    // 设置部门信息
                    if (dept != null) {
                        vo.setDeptId(dept.getDeptId());
                        vo.setDeptName(dept.getDeptName());
                    }
                } catch (NumberFormatException e) {
                    // 忽略转换异常
                }
            }

            // 从Map中获取当前用户的参加状态
            ActivityParticipants participant = activityParticipantsMap.get(activity.getId().toString());

            // 设置参加状态
            if (participant != null) {
                vo.setIsAttend(participant.getIsAttend());
                vo.setNoAttendReason(participant.getNoAttendReason());

                // 设置参加状态文本
                switch (participant.getIsAttend()) {
                    case "1":
                        vo.setAttendStatusText("参加");
                        break;
                    case "0":
                        vo.setAttendStatusText("不参加");
                        break;
                    case "2":
                        vo.setAttendStatusText("请假");
                        break;
                    default:
                        vo.setAttendStatusText("");
                }
            }

            return vo;
        });

        return voPage;
    }

    /**
     * 用户报名参加活动
     *
     * @param userId          用户ID
     * @param registrationDTO 报名信息
     * @return 是否报名成功
     */
    @Override
    public boolean registerActivity(String userId, ActivityRegistrationDTO registrationDTO) {
        if (StringUtils.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }

        List<CommitteeMember> committeeMember = committeeMemberMapper.selectList(new LambdaQueryWrapper<CommitteeMember>()
                .eq(CommitteeMember::getUserId, userId)
                .orderByDesc(CommitteeMember::getYear));
        if (committeeMember == null) {
            throw new ServiceException("您不是委员会成员，无法报名");
        }


        // 1. 查询活动信息，检查活动是否存在
        ActivityBasicinfo activity = activityBasicinfoMapper.selectById(registrationDTO.getActivityId());
        if (activity == null) {
            throw new ServiceException("活动不存在");
        }

        // 2. 检查活动状态是否为已发布
        if (!"1".equals(activity.getStatus())) {
            throw new ServiceException("活动未发布，无法报名");
        }

        // 3. 检查活动是否在报名时间内
        Date now = new Date();
        if (activity.getEnterBeginDate() != null && now.before(activity.getEnterBeginDate())) {
            throw new ServiceException("活动报名未开始");
        }

        if (activity.getEnterEndDate() != null && now.after(activity.getEnterEndDate())) {
            throw new ServiceException("活动报名已结束");
        }

        // 4. 检查用户是否已经报名过该活动
        LambdaQueryWrapper<ActivityParticipants> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityParticipants::getActivityPkid, registrationDTO.getActivityId());
        queryWrapper.eq(ActivityParticipants::getPepolePkid, committeeMember.get(0).getId().toString());
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new ServiceException("您已经报名过该活动");
        }

        // 5. 创建报名记录
        ActivityParticipants participant = new ActivityParticipants();
        participant.setPkid(UUID.randomUUID().toString());
        participant.setActivityPkid(registrationDTO.getActivityId());
        participant.setPepolePkid(committeeMember.get(0).getId().toString());
        participant.setIsAttend("1"); // 默认参加
        participant.setCreateId(userId);
        participant.setCreateTime(now);
        participant.setIsLeader("0"); // 默认非负责人

        // 如果有备注信息，则设置为不参加原因（可用于记录报名备注）
        if (StringUtils.isNotEmpty(registrationDTO.getRemark())) {
            participant.setNoAttendReason(registrationDTO.getRemark());
        }

        this.save(participant);

        List<ActivitySign> activitySignList = activitySignMapper.selectList(new LambdaQueryWrapper<ActivitySign>().eq(ActivitySign::getActivityPkid, activity.getId()));
        if (activitySignList != null && !activitySignList.isEmpty()) {
            for (ActivitySign activitySign : activitySignList) {
                ActivitySignDetail activitySignDetail = activitySignDetailMapper.selectOne(new LambdaQueryWrapper<ActivitySignDetail>()
                .eq(ActivitySignDetail::getSignPkid, activitySign.getId().toString())
                .eq(ActivitySignDetail::getPepolePkid, committeeMember.get(0).getId().toString()));
                if (activitySignDetail == null) {
                    activitySignDetail = new ActivitySignDetail();
                    activitySignDetail.setPkid(UUID.randomUUID().toString());
                    activitySignDetail.setSignPkid(activitySign.getId().toString());
                    activitySignDetail.setPepolePkid(committeeMember.get(0).getId().toString());
                    activitySignDetail.setActivityPkid(activity.getId().toString());
                    activitySignDetail.setBeginDate(activitySign.getSignBeginDate());
                    activitySignDetail.setIsSign("0"); // 默认未签到
                    activitySignDetailMapper.insert(activitySignDetail);
                }
            }
        }

        // 6. 保存报名记录
        return true;
    }

    /**
     * 根据活动ID查询当前登录用户参加活动的详细情况
     *
     * @param activityId 活动ID
     * @param userId 当前登录用户ID
     * @return 参与详情
     */
    @Override
    public ActivityParticipantsVO getParticipantDetailByActivityAndCurrentUser(String activityId, String userId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new ServiceException("活动ID不能为空");
        }
        if (StringUtils.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }

        // 根据用户ID查找对应的委员会成员信息
        List<CommitteeMember> committeeMember = committeeMemberMapper.selectList(new LambdaQueryWrapper<CommitteeMember>()
                .eq(CommitteeMember::getUserId, userId)
                .orderByDesc(CommitteeMember::getYear));

        // 使用委员会成员ID作为人员ID查询参与详情
        String peopleId = committeeMember.get(0).getId().toString();

        // 调用Mapper方法查询详细信息（包含人员姓名和职务）
        ActivityParticipantsVO participantDetail = this.baseMapper.getParticipantDetailByActivityAndPeople(activityId, peopleId);

//        if (participantDetail == null) {
//            throw new ServiceException("未找到您在此活动中的参与记录");
//        }

        return participantDetail;
    }
}