package com.ruoyi.project.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.activity.SignStatusEnum;
import com.ruoyi.common.enums.committee.SignTypeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.ActivitySignDetail;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailBatchDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailLeaveDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailPageDTO;
import com.ruoyi.project.activity.domain.dto.UpdateActivitySignDetailDTO;
import com.ruoyi.project.activity.domain.dto.UserActivitySignDetailDTO;
import com.ruoyi.project.activity.domain.dto.UserUpdateActivitySignDetailStatusDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySignDetailVO;
import com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO;
import com.ruoyi.project.activity.mapper.ActivityBasicinfoMapper;
import com.ruoyi.project.activity.mapper.ActivityParticipantsMapper;
import com.ruoyi.project.activity.mapper.ActivitySignDetailMapper;
import com.ruoyi.project.activity.service.IActivitySignDetailService;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 活动签到明细服务实现
 */
@Service
public class ActivitySignDetailServiceImpl extends ServiceImpl<ActivitySignDetailMapper, ActivitySignDetail>
        implements IActivitySignDetailService {

    @Autowired
    private ActivitySignDetailMapper activitySignDetailMapper;

    @Autowired
    private ActivityBasicinfoMapper activityBasicinfoMapper;

    @Autowired
    private ActivityParticipantsMapper activityParticipantsMapper;

    @Autowired
    private CommitteeMemberMapper committeeMemberMapper;

    @Override
    public IPage<ActivitySignDetailVO> getSignDetailPage(ActivitySignDetailPageDTO dto) {
        // 创建分页对象
        Page<ActivitySignDetailVO> page = new Page<>(dto.getCurrentPage(), dto.getPageSize());

        // 使用自定义的Mapper方法进行查询，支持人员姓名的模糊查询
        return activitySignDetailMapper.getSignDetailPageWithName(page, dto.getSignPkid(), dto.getPeopleName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSignIn(ActivitySignDetailBatchDTO dto) {
        // 获取当前登录用户
        String username = SecurityUtils.getUsername();
        Date now = new Date();

        // 判断当前登录用户是否为admin，设置不同的签到类型
        String signType = "1"; // 默认为普通签到
        if ("admin".equals(username)) {
            signType = "3"; // admin用户为工作人员代签
        }

        int successCount = 0;

        // 遍历需要签到的人员ID列表
        for (String peoplePkid : dto.getPeoplePkids()) {
            // 查询是否存在该人员的签到明细记录
            LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ActivitySignDetail::getSignPkid, dto.getSignPkid());
            queryWrapper.eq(ActivitySignDetail::getPepolePkid, peoplePkid);
            ActivitySignDetail detail = this.getOne(queryWrapper);

            if (detail != null) {
                // 如果已存在记录，则更新签到状态
                LambdaUpdateWrapper<ActivitySignDetail> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ActivitySignDetail::getId, detail.getId());
                updateWrapper.set(ActivitySignDetail::getIsSign, "1"); // 设置为已签到
                updateWrapper.set(ActivitySignDetail::getSignType, signType); // 设置签到类型
                updateWrapper.set(ActivitySignDetail::getBeginDate, now); // 设置签到开始时间
                updateWrapper.set(ActivitySignDetail::getIsLeave, "0"); // 设置为未请假
                updateWrapper.set(ActivitySignDetail::getReason, null); // 设置请假理由

                if (dto.getRemark() != null && !dto.getRemark().isEmpty()) {
                    updateWrapper.set(ActivitySignDetail::getReason, dto.getRemark()); // 设置备注
                }

                if (this.update(updateWrapper)) {
                    successCount++;
                }
            }
        }

        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean leaveRequest(ActivitySignDetailLeaveDTO dto) {
        // 查询是否存在该人员的签到明细记录
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySignDetail::getSignPkid, dto.getSignPkid());
        queryWrapper.eq(ActivitySignDetail::getPepolePkid, dto.getPeoplePkid());
        ActivitySignDetail detail = this.getOne(queryWrapper);

        if (detail == null) {
            throw new RuntimeException("未找到对应的签到记录");
        }

        // 更新请假状态
        LambdaUpdateWrapper<ActivitySignDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ActivitySignDetail::getId, detail.getId());
        updateWrapper.set(ActivitySignDetail::getIsLeave, "1"); // 设置为已请假
        updateWrapper.set(ActivitySignDetail::getReason, dto.getReason()); // 设置请假理由
        updateWrapper.set(ActivitySignDetail::getBeginDate, new Date()); // 设置签到开始时间

        return this.update(updateWrapper);
    }

    @Override
    public IPage<UserActivitySignDetailVO> getCurrentUserActivitySignDetails(UserActivitySignDetailDTO dto) {
        // 获取当前登录用户ID
        String userId = SecurityUtils.getUserId().toString();

        // 查询当前登录用户在当前活动中的委员id列表
        List<String> committeeIds = activityParticipantsMapper.getCommitteeIdsByActivityAndUser(dto.getActivityPkid(),
                userId);
        if (committeeIds == null || committeeIds.isEmpty()) {
            throw new ServiceException("您不是当前活动的委员，无法查看签到详情");
        }

        // 创建分页对象
        Page<UserActivitySignDetailVO> page = new Page<>(dto.getCurrentPage(), dto.getPageSize());

        // 对返回的数据进行一下处理，翻译一下签到类型的字典
        IPage<UserActivitySignDetailVO> pageResult = activitySignDetailMapper.getUserActivitySignDetails(page,
                dto.getActivityPkid(), committeeIds, dto.getSignDesc());
        pageResult.getRecords().forEach(detail -> {
            try {
                if (StringUtils.isNotEmpty(detail.getSignType()) && StringUtils.isNotEmpty(detail.getSignType())) {
                    SignTypeEnum signTypeEnum = SignTypeEnum.getDescriptionByCode(detail.getSignType());
                    detail.setSignType(signTypeEnum.getCode());
                    detail.setSignTypeLabel(signTypeEnum.getDescription());
                }
            } catch (Exception e) {
                // Ignore
            }
        });
        return pageResult;
    }

    @Override
    public boolean updateUserActivitySignDetails(UpdateActivitySignDetailDTO dto) {
        // 获取当前登录用户
        String userId = SecurityUtils.getUserId().toString();

        // 查询当前用户是否为委员
        List<CommitteeMember> committeeMember = committeeMemberMapper
                .selectList(new LambdaQueryWrapper<CommitteeMember>()
                        .eq(CommitteeMember::getUserId, userId)
                        .orderByDesc(CommitteeMember::getYear));
        if (committeeMember == null) {
            throw new ServiceException("您不是委员会成员，无法查看可以报名的活动");
        }

        // 根据活动ID和人员ID查询签到明细
        LambdaQueryWrapper<ActivityBasicinfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityBasicinfo::getId, dto.getActivityPkid());
        ActivityBasicinfo activityBasicinfo = activityBasicinfoMapper.selectOne(queryWrapper);

        if (activityBasicinfo == null) {
            throw new RuntimeException("未找到对应的活动");
        }

        // 根据活动ID和人员ID查询活动明细
        LambdaQueryWrapper<ActivityParticipants> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(ActivityParticipants::getActivityPkid, dto.getActivityPkid());
        queryWrapper1.eq(ActivityParticipants::getPepolePkid, committeeMember.get(0).getId().toString());
        ActivityParticipants activityParticipants = activityParticipantsMapper.selectOne(queryWrapper1);

        if (activityParticipants != null) {
            activityParticipants.setIsAttend(dto.getStatus());
            if ((dto.getStatus().equals("0") || dto.getStatus().equals("2")) && dto.getReason().isEmpty()) {
                throw new RuntimeException("请假原因不能为空");
            }
            activityParticipants.setNoAttendReason(dto.getReason());
            activityParticipantsMapper.updateById(activityParticipants);
        }

        return true;
    }

    @Override
    public boolean updateUserActivitySignDetailsStatus(UserUpdateActivitySignDetailStatusDTO dto) {
        // 获取当前登录用户
        // String userId = SecurityUtils.getUserId().toString();

        // 根据签到ID查询一下签到信息是否存在
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySignDetail::getId, dto.getSignDetailId());
        ActivitySignDetail activitySignDetail = this.getOne(queryWrapper);

        if (activitySignDetail == null) {
            throw new RuntimeException("未找到对应的签到记录");
        }

        // 更新签到状态
        LambdaUpdateWrapper<ActivitySignDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ActivitySignDetail::getId, dto.getSignDetailId());
        updateWrapper.set(ActivitySignDetail::getBeginDate, new Date()); // 设置签到/请假时间

        if (dto.getSignStatus().equals(SignStatusEnum.SIGNED)) {
            updateWrapper.set(ActivitySignDetail::getIsSign, "1"); // 设置为已签到
            updateWrapper.set(ActivitySignDetail::getSignType, SignTypeEnum.PC.getCode()); // 设置签到类型
            updateWrapper.set(ActivitySignDetail::getIsLeave, "0"); // 设置为未请假
            updateWrapper.set(ActivitySignDetail::getReason, null); // 设置请假理由
        }

        if (dto.getSignStatus().equals(SignStatusEnum.LEAVE)) {
            if (dto.getReason().isEmpty()) {
                throw new RuntimeException("请假原因不能为空");
            }
            updateWrapper.set(ActivitySignDetail::getIsLeave, "1"); // 设置为已请假
            updateWrapper.set(ActivitySignDetail::getReason, dto.getReason()); // 设置请假理由
            updateWrapper.set(ActivitySignDetail::getIsSign, "0"); // 设置为未签到
            updateWrapper.set(ActivitySignDetail::getSignType, null); // 设置签到类型
        }

        return this.update(updateWrapper);
    }

    @Override
    public boolean resetSignStatus(String signDetailId) {
        if (StringUtils.isEmpty(signDetailId)) {
            throw new RuntimeException("签到明细ID不能为空");
        }

        // 更新签到状态
        LambdaUpdateWrapper<ActivitySignDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ActivitySignDetail::getId, signDetailId);
        updateWrapper.set(ActivitySignDetail::getIsSign, "0"); // 设置为未签到
        updateWrapper.set(ActivitySignDetail::getIsLeave, "0"); // 设置为未请假
        updateWrapper.set(ActivitySignDetail::getReason, null); // 设置请假理由
        updateWrapper.set(ActivitySignDetail::getSignType, null); // 设置签到类型
        updateWrapper.set(ActivitySignDetail::getBeginDate, null); // 设置签到/请假时间

        return this.update(updateWrapper);
    }
}