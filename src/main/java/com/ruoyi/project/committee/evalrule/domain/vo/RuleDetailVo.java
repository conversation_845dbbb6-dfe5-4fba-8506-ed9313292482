package com.ruoyi.project.committee.evalrule.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 规则详情VO
 */
@Data
@ApiModel(value = "规则详情VO", description = "规则详情视图对象")
public class RuleDetailVo {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String pkid;

    /** 规则ID */
    @ApiModelProperty(value = "规则ID")
    private String rulePkid;

    /** 根节点ID */
    @ApiModelProperty(value = "根节点ID")
    private String rootPkid;

    /** 是否根节点 */
    @ApiModelProperty(value = "是否根节点")
    private String isRoot;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 得分 */
    @ApiModelProperty(value = "得分")
    private String score;

    /** 排序 */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /** 关联数据 */
    @ApiModelProperty(value = "关联数据")
    private String associated;

    /** 最低分 */
    @ApiModelProperty(value = "最低分")
    private String limitMinScore;

    /** 最高分 */
    @ApiModelProperty(value = "最高分")
    private String limitMaxScore;

    /** 是否自主填报项 */
    @ApiModelProperty(value = "是否自主填报项")
    private Boolean isDeclare;
    
    /** 子规则列表 */
    @ApiModelProperty(value = "子规则列表")
    private List<RuleDetailVo> children;
}
