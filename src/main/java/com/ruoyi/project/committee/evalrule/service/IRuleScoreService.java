package com.ruoyi.project.committee.evalrule.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.committee.evalrule.domain.RuleScore;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;

public interface IRuleScoreService {

    /**
     * 委员规则得分分页查询
     * @param pageDto pageDto
     * @return result
     */
    IPage<RuleScore> selectRuleScorePage(RuleScorePageDto pageDto);

    /**
     * 委员规则得分详情
     * @param id id
     * @return result
     */
    RuleScore getById(String id);


    /**
     * 计算得分
     * @return affectRows
     */
    Integer calculateScore(Integer year);
}
