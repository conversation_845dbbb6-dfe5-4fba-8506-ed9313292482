package com.ruoyi.project.committee.evalrule.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.collections.TripleMap;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.evalrule.domain.RuleScore;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;
import com.ruoyi.project.committee.evalrule.mapper.RuleScoreMapper;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import com.ruoyi.project.committee.evalrule.service.IRuleScoreService;
import com.ruoyi.project.committee.evalrule.service.IRuleStrategyService;
import com.ruoyi.project.committee.evalrule.strategy.BasicStrategy;
import com.ruoyi.project.committee.evalrule.strategy.RewardStrategy;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class RuleScoreServiceImpl extends ServiceImpl<RuleScoreMapper, RuleScore> implements IRuleScoreService {

    @Resource
    private RuleScoreMapper ruleScoreMapper;

    @Resource
    private IRuleStrategyService ruleStrategyService;

    @Resource
    private IRuleInfoService ruleInfoService;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    private BasicStrategy basicStrategy;
    private RewardStrategy rewardStrategy;

    private final Map<String, Method> methodCache = new ConcurrentHashMap<>();
    private static final String STRATEGY_TYPE_BASIC = "BASIC";
    private static final String STRATEGY_TYPE_REWARD = "REWARD";

    @PostConstruct
    public void init() {
        this.basicStrategy = SpringUtils.getBean(BasicStrategy.class);
        this.rewardStrategy = SpringUtils.getBean(RewardStrategy.class);
    }


    @Override
    public IPage<RuleScore> selectRuleScorePage(RuleScorePageDto pageDto) {
        if (ObjectUtil.isNull(pageDto.getYear())) {
            pageDto.setYear(DateUtil.thisYear());
        }
        Page<RuleScore> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        return ruleScoreMapper.selectRuleScorePage(page, pageDto);
    }

    @Override
    public RuleScore getById(String id) {
        return ruleScoreMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer calculateScore(Integer year) {
        // 获取委员信息
        List<CommitteeMember> memberList = committeeMemberMapper.selectMemberByYear(year);
        if (ObjectUtil.isEmpty(memberList)) {
            throw new ServiceException("未找到" + year + "年度的委员信息.");
        }

        // 获取规则信息
        RuleInfoVo ruleInfo = ruleInfoService.selectRuleInfoByYear(year);
        if (ObjectUtil.isEmpty(ruleInfo)) {
            throw new ServiceException("未找到" + year + "年度的规则信息.");
        }

        TripleMap<String, String, String> strategyMap = ruleStrategyService.getStrategyMap();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("计算规则分数");

        List<RuleScore> ruleScoreList = calculateScore(memberList, ruleInfo, strategyMap);

        stopWatch.stop();
        log.info("计算规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        long nonZeroScores = ruleScoreList.stream().mapToInt(RuleScore::getTotalScore).filter(score -> score > 0).count();
        log.info("计算完成，有分数的委员数量: {}/{}", nonZeroScores, ruleScoreList.size());

        stopWatch.start("保存规则分数");
        Integer affectRows = upsertRuleScoresBatch(ruleScoreList);
        stopWatch.stop();
        log.info("保存规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        return affectRows;
    }

    private List<RuleScore> calculateScore(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                           TripleMap<String, String, String> strategyMap) {
        List<RuleScore> ruleScoreList = new ArrayList<>();

        for (CommitteeMember member : memberList) {
            // 为每个委员创建一个累加器
            ScoreAccumulator accumulator = new ScoreAccumulator();

            for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
                // 重置最终得分
                resetFinalScores(ruleDetailVo);
                // 计算每个规则的得分
                evaluateLeafNodes(member, ruleDetailVo, strategyMap);

                // 将当前规则的分数累加到委员的累加器中
                accumulateScoresByStrategyType(ruleDetailVo, strategyMap, accumulator);
            }

            // 从累加器中获取最终分数
            Integer basicScore = accumulator.getBasicScore();
            Integer rewardScore = accumulator.getRewardScore();
            Integer totalScore = basicScore + rewardScore;

            RuleScore ruleScore = new RuleScore();
            ruleScore.setId(member.getId());
            ruleScore.setYear(Integer.valueOf(member.getYear()));
            ruleScore.setUserId(Long.valueOf(member.getUserId()));
            ruleScore.setUserName(member.getUserName());
            ruleScore.setNumberId(member.getNumberId());
            ruleScore.setUnitPost(member.getUnitPost());
            ruleScore.setBasicScore(basicScore);
            ruleScore.setRewardScore(rewardScore);
            ruleScore.setTotalScore(totalScore);
            ruleScore.setScoreDetail(JSON.toJSONString(ruleInfo));
            ruleScoreList.add(ruleScore);
        }

        return ruleScoreList;
    }



    private void evaluateLeafNodes(CommitteeMember member, RuleDetailVo ruleDetail,
                                  TripleMap<String, String, String> strategyMap) {
        if (ruleDetail == null) return;

        if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
            if (ruleDetail.getStrategyKey() != null) {
                Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
                if (strategyPair != null) {
                    try {
                        Integer score = calculateScoreByStrategy(member, ruleDetail, strategyPair);
                        ruleDetail.setFinalScore(score);
                    } catch (Exception e) {
                        log.error("Execute Method[{}] failed: ", strategyPair.getValue(), e);
                        ruleDetail.setFinalScore(0);
                    }
                }
            }
        } else {
            int childrenTotalScore = 0;
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                evaluateLeafNodes(member, child, strategyMap);
                childrenTotalScore += Optional.ofNullable(child.getFinalScore()).orElse(0);
            }
            ruleDetail.setFinalScore(childrenTotalScore);
        }
    }

    private Integer calculateScoreByStrategy(CommitteeMember member, RuleDetailVo ruleDetail,
                                            Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        if (methodName == null) return 0;

        switch (ruleType) {
            case "BASIC":
                Method basicMethod = getOrCacheMethod(STRATEGY_TYPE_BASIC, methodName);
                basicMethod.invoke(basicStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            case "REWARD":
                Method rewardMethod = getOrCacheMethod(STRATEGY_TYPE_REWARD, methodName);
                rewardMethod.invoke(rewardStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            default:
                return 0;
        }
    }



    private void resetFinalScores(RuleDetailVo ruleDetail) {
        if (ruleDetail == null) return;

        ruleDetail.setFinalScore(null);

        if (ruleDetail.getChildren() != null) {
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                resetFinalScores(child);
            }
        }
    }

    private Method getOrCacheMethod(String ruleType, String methodName) throws NoSuchMethodException {
        String cacheKey = ruleType + ":" + methodName;
        return methodCache.computeIfAbsent(cacheKey, key -> {
            try {
                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    return basicStrategy.getClass().getMethod(methodName,
                           CommitteeMember.class, RuleDetailVo.class);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    return rewardStrategy.getClass().getMethod(methodName,
                           CommitteeMember.class, RuleDetailVo.class);
                }
                throw new IllegalArgumentException("Unknown strategy type: " + ruleType);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException("Method not found: " + methodName, e);
            }
        });
    }

    /**
     * 分数累加器，用于按策略类型累加分数
     */
    @Getter
    private static class ScoreAccumulator {
        private Integer basicScore = 0;
        private Integer rewardScore = 0;

        public void addBasicScore(Integer score) {
            if (score != null) {
                basicScore += score;
            }
        }

        public void addRewardScore(Integer score) {
            if (score != null) {
                rewardScore += score;
            }
        }

    }

    /**
     * 根据策略类型累加分数
     */
    private void accumulateScoresByStrategyType(RuleDetailVo ruleDetail,
                                              TripleMap<String, String, String> strategyMap,
                                              ScoreAccumulator accumulator) {
        if (ruleDetail == null) return;

        // 如果是叶子节点且有策略键，根据策略类型累加分数
        if (ObjectUtil.isEmpty(ruleDetail.getChildren()) && ruleDetail.getStrategyKey() != null) {
            Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
            if (strategyPair != null) {
                String ruleType = strategyPair.getKey();
                Integer finalScore = ruleDetail.getFinalScore();

                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    accumulator.addBasicScore(finalScore);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    accumulator.addRewardScore(finalScore);
                }
            }
        } else if (ruleDetail.getChildren() != null) {
            // 递归处理子节点
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                accumulateScoresByStrategyType(child, strategyMap, accumulator);
            }
        }
    }

    private Integer upsertRuleScoresBatch(List<RuleScore> ruleScoreList) {
        if (ObjectUtil.isEmpty(ruleScoreList)) {
            return 0;
        }

        // 检查数据唯一性，避免重复插入
        Set<Long> idSet = new HashSet<>();
        List<RuleScore> uniqueList = new ArrayList<>();

        for (RuleScore score : ruleScoreList) {
            if (idSet.add(score.getId())) {
                uniqueList.add(score);
            }
        }


        log.info("实际处理数据量: {}", uniqueList.size());
        try {
            int batchSize = 50;
            int totalAffected = 0;

            List<List<RuleScore>> batches = Lists.partition(uniqueList, batchSize);
            log.info("分批处理，批次数: {}, 每批大小: {}", batches.size(), batchSize);

            for (int i = 0; i < batches.size(); i++) {
                List<RuleScore> batch = batches.get(i);
                try {
                    int affected = ruleScoreMapper.upsertRuleScoresBatch(batch);
                    int processedRecords = Math.min(affected, batch.size()); // 实际处理的记录数

                    totalAffected += processedRecords;
                    log.debug("批次 {}/{} 完成，MySQL影响行数: {}, 实际处理记录数: {}",
                             i + 1, batches.size(), affected, processedRecords);
                } catch (Exception e) {
                    log.error("批次 {}/{} 失败，批次大小: {}", i + 1, batches.size(), batch.size(), e);
                    // 单条重试，但要避免重复计数
                    for (RuleScore item : batch) {
                        try {
                            int singleAffected = ruleScoreMapper.upsertRuleScoresBatch(Collections.singletonList(item));
                            int singleProcessed = Math.min(singleAffected, 1);
                            totalAffected += singleProcessed;
                        } catch (Exception singleE) {
                            log.error("单条插入失败，委员: {} (ID: {})", item.getUserName(), item.getId(), singleE);
                        }
                    }
                }
            }

            log.info("批量保存完成，总影响行数: {}", totalAffected);
            return totalAffected;

        } catch (Exception e) {
            log.error("批量保存失败", e);
            throw new ServiceException("批量保存规则分数失败: " + e.getMessage());
        }
    }
}
