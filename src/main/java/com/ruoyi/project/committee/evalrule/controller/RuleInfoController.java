package com.ruoyi.project.committee.evalrule.controller;


import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoEditDto;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 规则信息 控制器
 */
@Api(tags = "规则信息管理")
@RestController
@RequestMapping("/committee/evalrule")
public class RuleInfoController extends BaseController {

    @Autowired
    private IRuleInfoService ruleInfoService;

    /**
     * 查询规则信息树形列表
     */
    @ApiOperation("查询规则信息树形列表")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:list')")
    @GetMapping("/tree")
    public TableDataInfo tree() {
        return getDataTable(ruleInfoService.selectRuleInfoList());
    }

    /**
     * 获取规则信息详细信息（包含树形结构）
     */
    @ApiOperation("获取规则信息详细信息（包含树形结构）")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:query')")
    @GetMapping("/{pkid}")
    public AjaxResult getInfo(@PathVariable("pkid") String pkid) {
        return success(ruleInfoService.selectRuleInfoById(pkid));
    }

    /**
     * 新增规则信息
     */
    @ApiOperation("保存规则信息")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:add')")
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdateRule(@RequestBody RuleInfoEditDto editDto) {
        return AjaxResult.success(ruleInfoService.saveOrUpdateRule(editDto));
    }


    /**
     * 生成新一年的规则（自动复用前一年的规则）
     */
    @ApiOperation("生成新一年的规则")
//    @PreAuthorize("@ss.hasPermi('committee:evalrule:gen')")
    @PostMapping("/generate")
    public AjaxResult generateNewYear() {
        String newRuleId = ruleInfoService.generateNewYearRule();
        return success(newRuleId);
    }
}
