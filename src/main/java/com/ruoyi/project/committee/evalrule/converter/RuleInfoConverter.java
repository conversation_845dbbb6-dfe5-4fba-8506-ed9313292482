package com.ruoyi.project.committee.evalrule.converter;

import com.ruoyi.project.committee.evalrule.domain.RuleDetail;
import com.ruoyi.project.committee.evalrule.domain.RuleInfo;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoDto;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleInfoEditDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 规则信息转换器
 */
@Mapper
public interface RuleInfoConverter {

    RuleInfoConverter INSTANCE = Mappers.getMapper(RuleInfoConverter.class);

    RuleInfo convertToRule(RuleInfoEditDto editDto);

    RuleDetail convertToDetail(RuleInfoEditDto editDto);

    /**
     * RuleInfoDTO转RuleInfo实体
     */
    RuleInfo dtoToEntity(RuleInfoDto dto);

    /**
     * RuleInfo实体转RuleInfoVO
     */
    RuleInfoVo entityToVo(RuleInfo entity);

    /**
     * RuleInfo实体列表转RuleInfoVO列表
     */
    List<RuleInfoVo> entityListToVoList(List<RuleInfo> entityList);

    /**
     * RuleDetail实体转RuleDetailVO
     */
    RuleDetailVo detailToVo(RuleDetail detail);

    /**
     * RuleDetail实体列表转RuleDetailVO列表
     */
    List<RuleDetailVo> detailListToVoList(List<RuleDetail> detailList);

}
