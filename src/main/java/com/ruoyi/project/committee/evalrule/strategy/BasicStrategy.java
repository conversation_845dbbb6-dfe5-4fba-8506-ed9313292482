package com.ruoyi.project.committee.evalrule.strategy;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BasicStrategy {

    private final BasicStrategyMapper basicStrategyMapper;


    /**
     * 缺席区政协全体会议1次（未请假）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAbsencePlenaryUnexcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAbsencePlenaryUnexcused(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协全体会议1次（请假）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAbsencePlenaryExcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAbsencePlenaryExcused(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协常委会议1次（未请假）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAbsenceStandingUnexcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAbsenceStandingUnexcused(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协常委会议1次（请假）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAbsenceStandingExcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAbsenceStandingExcused(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协专委会会议1次（未请假）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAbsenceCommitteeUnexcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAbsenceCommitteeUnexcused(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协专委会会议1次（请假）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAbsenceCommitteeExcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAbsenceCommitteeExcused(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 区政协委员列席区政协常委扩大会议
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAttendanceStandingExtended(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAttendanceStandingExtended(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 参加专委会组织的其他会议（非全体会议）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAttendanceCommitteeOther(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAttendanceCommitteeOther(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 参加全国政协、省政协、市政协组织的相关会议
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void handleAttendanceHigherMeeting(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.handleAttendanceHigherMeeting(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }
    
    
    /**
     * 一年内不提交提案
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void hasNotSubmittedProposalWithinOneYear(CommitteeMember member, RuleDetailVo ruleDetail) {
        if (basicStrategyMapper.hasNotSubmittedProposalWithinOneYear(member)) {
            ruleDetail.setFinalScore(Integer.valueOf(ruleDetail.getScore()));
        } else {
            ruleDetail.setFinalScore(0);
        }
    }

    /**
     * 获立案的个人提案（第一提案人）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countApprovedProposalsAsPrimaryProposer(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countApprovedProposalsAsPrimaryProposer(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 获立案的个人提案（附议人）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countApprovedProposalsAsSecondaryProposer(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countApprovedProposalsAsSecondaryProposer(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 1年内不提交社情民意（按年度累计计分）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void hasNotSubmittedManuscriptWithinOneYear(CommitteeMember member, RuleDetailVo ruleDetail) {
        if (basicStrategyMapper.hasNotSubmittedManuscriptWithinOneYear(member)) {
            ruleDetail.setFinalScore(Integer.valueOf(ruleDetail.getScore()));
        } else {
            ruleDetail.setFinalScore(0);
        }
    }

    /**
     * 社情民意被采纳
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countAcceptedManuscripts(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countAcceptedManuscripts(member);
        int finalScore = count * Integer.parseInt(ruleDetail.getScore());
        ruleDetail.setFinalScore(Math.min(finalScore, 6));
    }

    /**
     * 获得区领导批示
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countDistrictEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countDistrictEndorsedManuscript(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 获得市领导批示
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countCityEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countCityEndorsedManuscript(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }


    /**
     * 获得省领导批示
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countProvinceEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countProvinceEndorsedManuscript(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }
}
