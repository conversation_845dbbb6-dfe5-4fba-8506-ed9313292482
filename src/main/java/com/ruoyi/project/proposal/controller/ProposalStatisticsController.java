package com.ruoyi.project.proposal.controller;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.poi.WordUtil;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.proposal.domain.dto.ProposalStatisticsDto;
import com.ruoyi.project.proposal.domain.vo.HandleCaseCountVo;
import com.ruoyi.project.proposal.domain.vo.HandleProgressStatsVo;
import com.ruoyi.project.proposal.domain.vo.HandleStatisticsVo;
import com.ruoyi.project.proposal.domain.vo.MemberProposalStatisticsVo;
import com.ruoyi.project.proposal.domain.vo.MemberProposalDetailStatisticsVo;
import com.ruoyi.project.proposal.service.impl.ProposalStatisticsService;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提案统计
 */
@RestController
@RequestMapping("/proposal/statistics")
public class ProposalStatisticsController extends BaseController {
    
    @Resource
    private ProposalStatisticsService proposalStatisticsService;

    /**
     * 办理结果统计
     * @param statisticsDto 统计参数
     * @return result
     */
    @PostMapping("/analyzeConsultResult")
    public TableDataInfo analyzeConsultResult(@RequestBody ProposalStatisticsDto statisticsDto) {
        return getDataTable(proposalStatisticsService.analyzeConsultResult(statisticsDto));
    }

    /**
     * 办理结果统计导出
     * @param statisticsDto 统计参数
     * @param response response
     */
    @PostMapping("/exportConsultResult")
    public void exportConsultResult(@RequestBody ProposalStatisticsDto statisticsDto, HttpServletResponse response) {
        List<HandleStatisticsVo> statisticsList = proposalStatisticsService.analyzeConsultResult(statisticsDto);

        try {
            ClassPathResource templateFile = new ClassPathResource("template/handle_result_statistics_template.docx");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("year", statisticsDto.getStatisticalYear());
            dataMap.put("total", statisticsList.size());
            dataMap.put("statisticsList", statisticsList);

            XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(), dataMap);
            WordUtil.write("办理结果统计.docx", document, response);

        } catch (Exception e) {
            logger.info("办理结果统计数据导出失败！ >> {}", e.getMessage());
        }

    }

    /**
     * 单位件数统计
     * @param statisticsDto 统计参数
     * @return result
     */
    @PostMapping("/analyzeDeptProposal")
    public TableDataInfo analyzeDeptProposal(@RequestBody ProposalStatisticsDto statisticsDto) {
        return getDataTable(proposalStatisticsService.analyzeDeptProposalCount(statisticsDto));
    }


    /**
     * 单位件数统计导出
     * @param statisticsDto statisticsDto
     * @param response response
     */
    @PostMapping("/exportDeptProposal")
    public void exportDeptProposal(@RequestBody ProposalStatisticsDto statisticsDto, HttpServletResponse response) {
        proposalStatisticsService.exportDeptProposal(statisticsDto, response);
    }


    /**
     * 办理件数统计
     * @param statisticsDto 统计参数
     * @return result
     */
    @PostMapping("/analyzeByGovOrPartyDept")
    public TableDataInfo analyzeByGovOrPartyDept(@RequestBody ProposalStatisticsDto statisticsDto) {
        return getDataTable(proposalStatisticsService.analyzeByGovOrPartyDept(statisticsDto));
    }

    /**
     * 办理件数统计导出
     * @param statisticsDto 统计参数
     * @param response response
     */
    @PostMapping("/exportByGovOrPartyDept")
    public void exportByGovOrPartyDept(@RequestBody ProposalStatisticsDto statisticsDto, HttpServletResponse response) {
        List<HandleCaseCountVo> countVoList = proposalStatisticsService.analyzeByGovOrPartyDept(statisticsDto);

        try {
            ClassPathResource templateFile = new ClassPathResource("template/handle_case_count_template.docx");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("statisticalYear", statisticsDto.getStatisticalYear());
            dataMap.put("total", countVoList.size() - 1);
            dataMap.put("countVoList", countVoList);
            dataMap.put("date", DateUtil.format(DateUtil.date(), "yyyy年MM月dd日 HH:mm:ss"));

            XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(), dataMap);

            String fileName = "系统办理件数统计.docx";
            WordUtil.write(fileName, document, response);
        } catch (Exception e) {
            logger.info("办理件数统计数据导出失败！ >> {}", e.getMessage());
        }
    }


    /**
     * 办理进度统计
     * @param statisticsDto statisticsDto
     * @return result
     */
    @PostMapping("/analyzeHandleProgress")
    public TableDataInfo analyzeHandleProgress(@RequestBody ProposalStatisticsDto statisticsDto) {
        return getDataTable(proposalStatisticsService.analyzeHandleProgress(statisticsDto));
    }

    /**
     * 办理进度统计导出
     * @param statisticsDto statisticsDto
     * @param response response
     */
    @PostMapping("/exportHandleProgress")
    public void exportHandleProgress(@RequestBody ProposalStatisticsDto statisticsDto, HttpServletResponse response) {
        List<HandleProgressStatsVo> progressStatsList = proposalStatisticsService.analyzeHandleProgress(statisticsDto);
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (HandleProgressStatsVo progressStats : progressStatsList) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(progressStats);
            objectMap.put("statisticalYear", statisticsDto.getStatisticalYear());
            dataList.add(objectMap);
        }

        try {
            ClassPathResource templateFile = new ClassPathResource("template/handle_progress_template.docx");

            XWPFDocument document = WordExportUtil.exportWord07(templateFile.getPath(), dataList);
            String fileName = statisticsDto.getStatisticalYear() + "政协提案办理进度统计表.docx";
            WordUtil.write(fileName, document, response);
        } catch (Exception e) {
            logger.info("办理进度统计数据导出失败！ >> {}", e.getMessage());
        }
    }

    /**
     * 接收情况统计
     * @param statisticsDto statisticsDto
     * @return result
     */
    @PostMapping("/analyzeProposalReception")
    public TableDataInfo analyzeProposalReception(@RequestBody ProposalStatisticsDto statisticsDto) {
        return getDataTable(proposalStatisticsService.analyzeProposalReception(statisticsDto));
    }

    /**
     * 办理情况通报
     * @param statisticsDto statisticsDto
     * @return result
     */
    @PostMapping("/analyzeHandleNotice")
    public AjaxResult analyzeHandleNotice(@RequestBody ProposalStatisticsDto statisticsDto) {
        return AjaxResult.success(proposalStatisticsService.analyzeHandleNotice(statisticsDto));
    }

    /**
     * 委员提案统计
     * @param year 年份
     * @return result
     */
    @GetMapping("/analyzeMemberProposal")
    @ApiOperation("委员提案统计")
    public AjaxResult analyzeMemberProposal(@RequestParam Integer year) {
        List<MemberProposalStatisticsVo> result = proposalStatisticsService.analyzeMemberProposal(year);
        return AjaxResult.success(result);
    }

    /**
     * 委员提案统计导出
     * @param year 年份
     * @param response response
     */
    @GetMapping("/exportMemberProposal")
    @ApiOperation("委员提案统计导出")
    public void exportMemberProposal(@RequestParam Integer year, HttpServletResponse response) {
        proposalStatisticsService.exportMemberProposal(year, response);
    }

    /**
     * 委员立案详细统计（立案/不立案分类）
     * @param year 年份
     * @return result
     */
    @GetMapping("/analyzeMemberProposalDetail")
    @ApiOperation("委员立案详细统计")
    public AjaxResult analyzeMemberProposalDetail(@RequestParam Integer year) {
        List<MemberProposalDetailStatisticsVo> result = proposalStatisticsService.analyzeMemberProposalDetail(year);
        return AjaxResult.success(result);
    }

    /**
     * 委员立案详细统计导出
     * @param year 年份
     * @param response response
     */
    @GetMapping("/exportMemberProposalDetail")
    @ApiOperation("委员立案详细统计导出")
    public void exportMemberProposalDetail(@RequestParam Integer year, HttpServletResponse response) {
        proposalStatisticsService.exportMemberProposalDetail(year, response);
    }
}
