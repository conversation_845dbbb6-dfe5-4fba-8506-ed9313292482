package com.ruoyi.project.proposal.domain.vo;

import lombok.Data;

import java.util.List;

@Data
public class HandleProgressStatsVo {

    // 部门名称
    private String deptName;

    // 办理总数
    private Long handleCount = 0L;

    // 已报数量
    private Long handleReportCount = 0L;

    // 主办已报
    private ProgressStatsVo hostReport;

    // 主办未报
    private ProgressStatsVo hostUnReport;

    // 会办已报
    private ProgressStatsVo coordinatorReported ;

    // 会办未报
    private ProgressStatsVo coordinatorUnReported ;


    public HandleProgressStatsVo() {
        this.hostReport = new ProgressStatsVo();
        this.hostUnReport = new ProgressStatsVo();
        this.coordinatorReported = new ProgressStatsVo();
        this.coordinatorUnReported = new ProgressStatsVo();
    }

    public void setHostReportCount(Integer count) {
        this.hostReport.setCount(count);
    }
    public void setHostUnReportCount(Integer count) {
        this.hostUnReport.setCount(count);
    }
    public void setCoordinatorReportedCount(Integer count) {
        this.coordinatorReported.setCount(count);
    }
    public void setCoordinatorUnReportedCount(Integer count) {
        this.coordinatorUnReported.setCount(count);
    }

    public void setHostReportStr(String str) {
        this.hostReport.setIdStr(str);
    }
    public void setHostUnReportStr(String str) {
        this.hostUnReport.setIdStr(str);
    }
    public void setCoordinatorReportedStr(String str) {
        this.coordinatorReported.setIdStr(str);
    }
    public void setCoordinatorUnReportedStr(String str) {
        this.coordinatorUnReported.setIdStr(str);
    }

    // 新增方法：支持List.size()返回的int类型
    public void setHostReportCount(int count) {
        this.hostReport.setCount(count);
    }
    public void setHostUnReportCount(int count) {
        this.hostUnReport.setCount(count);
    }
    public void setCoordinatorReportedCount(int count) {
        this.coordinatorReported.setCount(count);
    }
    public void setCoordinatorUnReportedCount(int count) {
        this.coordinatorUnReported.setCount(count);
    }


}
