package com.ruoyi.project.community.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.enums.manuscript.*;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.community.converter.ManuscriptConverter;
import com.ruoyi.project.community.domain.*;
import com.ruoyi.project.community.domain.dto.ManuscriptAuditDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptResponseDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptPublishDTO;
import com.ruoyi.project.community.domain.dto.ManuscriptStatisticsDto;
import com.ruoyi.project.community.domain.vo.*;
import com.ruoyi.project.community.mapper.*;
import com.ruoyi.project.community.service.IManuscriptAnnexService;
import com.ruoyi.project.community.service.IManuscriptReflectorService;
import com.ruoyi.project.community.service.IManuscriptService;
import com.ruoyi.project.proposal.domain.dto.ProposalToManuscriptDto;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.domain.vo.ProposalUserRelVo;
import com.ruoyi.project.system.converter.SysDeptConverter;
import com.ruoyi.project.system.domain.SysDept;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.domain.vo.SysDeptVo;
import com.ruoyi.project.system.mapper.SysDeptMapper;
import com.ruoyi.project.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ManuscriptServiceImpl implements IManuscriptService {

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ManuscriptMapper manuscriptMapper;

    @Resource
    private ManuscriptContentMapper manuscriptContentMapper;

    @Resource
    private ManuscriptRecordMapper manuscriptRecordMapper;

    @Resource
    private ManuscriptFeedbackMapper manuscriptFeedbackMapper;

    @Resource
    private ManuscriptEndorsementMapper manuscriptEndorsementMapper;

    @Resource
    private IManuscriptAnnexService manuscriptAnnexService;

    @Resource
    private IManuscriptReflectorService manuscriptReflectorService;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    private static final Map<ManuscriptStatusEnum, String> STATUS_LABEL_MAP;
    static {
        Map<ManuscriptStatusEnum, String> map = new HashMap<>();
        map.put(ManuscriptStatusEnum.AUDITING, "未受理");
        map.put(ManuscriptStatusEnum.WAIT_FOR_ISSUE, "已受理");
        map.put(ManuscriptStatusEnum.ISSUED, "已采用");
        map.put(ManuscriptStatusEnum.FINISHED, "已采用");
        map.put(ManuscriptStatusEnum.RETURN_MODIFY, "已退回");
        map.put(ManuscriptStatusEnum.DISCARD, "不采纳");
        STATUS_LABEL_MAP = Collections.unmodifiableMap(map);
    }

    private void validate(String id) {
        if (ObjectUtil.isNull(manuscriptMapper.selectById(id))) {
            throw new ServiceException("该稿件不存在!", -1);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addManuscript(ManuscriptEditVo editVo) {

        Manuscript manuscript = ManuscriptConverter.INSTANCE.convert(editVo);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        manuscript.setYear(DateUtil.date().year());
        manuscript.setContact(loginUser.getUserId().toString());
        manuscript.setReflector(getReflector(editVo.getReflectorList()));
        manuscript.setStatus(ObjectUtil.isNull(editVo.getStatus()) ? ManuscriptStatusEnum.AUDITING : editVo.getStatus());
        manuscript.setIsPublish(false);

        if (ObjectUtil.isNotEmpty(editVo.getReportUnit())) {
            String reportUnit = editVo.getReportUnit().stream().map(ReportUnitEnum::name).collect(Collectors.joining(","));
            manuscript.setReportUnit(reportUnit);
        }

        String reportUnitDetail;
        if (ObjectUtil.isNotEmpty(editVo.getReportUnitDetail())) {
            reportUnitDetail = JSON.toJSONString(editVo.getReportUnitDetail());
        } else {
            reportUnitDetail = JSON.toJSONString(new ReportUnitVo(), JSONWriter.Feature.WriteNulls);
        }
        manuscript.setReportUnitDetail(reportUnitDetail);

        if (ObjectUtil.isNotEmpty(editVo.getReportTarget())) {
            String reportTarget = editVo.getReportTarget().stream().map(ReportTargetEnum::name).collect(Collectors.joining(","));
            manuscript.setReportTarget(reportTarget);
        }




        // 暂存
        if (ObjectUtil.isNotEmpty(editVo.getId())) {
            return editManuscript(editVo);
        } else {
            manuscriptMapper.insert(manuscript);
        }

        // 稿件正文
        manuscriptContentMapper.addManuscriptContent(manuscript.getId() ,editVo);

        // 反映人
        manuscriptReflectorService.saveBatchManuscriptReflector(manuscript.getId(), editVo.getReflectorList());

        // 附件
        manuscriptAnnexService.saveBatchManuscriptAnnex(manuscript.getId(), editVo.getFileList());

        return manuscript.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String editManuscript(ManuscriptEditVo editVo) {

        validate(editVo.getId());

        Manuscript manuscript = ManuscriptConverter.INSTANCE.convert(editVo);

        if (ObjectUtil.isNotEmpty(editVo.getReportUnit())) {
            String reportUnit = editVo.getReportUnit().stream().map(ReportUnitEnum::name).collect(Collectors.joining(","));
            manuscript.setReportUnit(reportUnit);
        }

        if (ObjectUtil.isNotEmpty(editVo.getReportUnitDetail())) {
            String reportUnitDetail = JSON.toJSONString(editVo.getReportUnitDetail());
            manuscript.setReportUnitDetail(reportUnitDetail);
        }

        if (ObjectUtil.isNotEmpty(editVo.getReportTarget())) {
            String reportTarget = editVo.getReportTarget().stream().map(ReportTargetEnum::name).collect(Collectors.joining(","));
            manuscript.setReportTarget(reportTarget);
        }

        // 稿件正文
        manuscriptContentMapper.updateManuscriptContent(editVo);

        // 反映人
        manuscript.setReflector(getReflector(editVo.getReflectorList()));
        manuscriptReflectorService.updateManuscriptReflector(manuscript.getId(), editVo.getReflectorList());

        // 附件
        manuscriptAnnexService.updateManuscriptAnnex(manuscript.getId(), editVo.getFileList());
        manuscriptMapper.updateById(manuscript);

        return manuscript.getId();
    }

    private String getReflector(List<String> reflectorIdList) {
        if (ObjectUtil.isNotEmpty(reflectorIdList)) {
            List<SysUser> sysUserList = sysUserService.selectUserByIds(reflectorIdList);

            return sysUserList.stream().map(SysUser::getUserName).collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public ManuscriptVo getManuscript(String id) {
        Manuscript manuscript = manuscriptMapper.selectById(id);
        ManuscriptVo manuscriptVo = ManuscriptConverter.INSTANCE.convertTo(manuscript);

        if (ObjectUtil.isNotNull(manuscript.getContact())) {
            SysUser sysUser = sysUserService.selectUserById(Long.valueOf(manuscript.getContact()));
            manuscriptVo.setContactName(sysUser.getUserName());
            manuscriptVo.setContactPhone(sysUser.getPhonenumber());
        }

        if (ObjectUtil.isNotEmpty(manuscript.getReportUnit())) {
            List<ReportUnitEnum> reportUnitList = Arrays.stream(manuscript.getReportUnit().split(",")).map(ReportUnitEnum::valueOf).collect(Collectors.toList());
            manuscriptVo.setReportUnit(reportUnitList);
        }

        if (ObjectUtil.isNotEmpty(manuscript.getReportUnitDetail())) {
            ReportUnitVo reportUnitVo = JSON.parseObject(manuscript.getReportUnitDetail(), ReportUnitVo.class);
            manuscriptVo.setReportUnitDetail(reportUnitVo);
            if (ObjectUtil.isNotEmpty(reportUnitVo.getWbHall())) {
                List<Long> deptIdList = Arrays.stream(reportUnitVo.getWbHall().split(",")).map(Long::parseLong).collect(Collectors.toList());
                List<SysDept> deptList = sysDeptMapper.selectListByIds(deptIdList);
                List<SysDeptVo> sysDeptVos = SysDeptConverter.INSTANCE.convertToList(deptList);
                manuscriptVo.setWbHall(sysDeptVos);
            }
        }

        if (ObjectUtil.isNotEmpty(manuscript.getReportTarget())) {
            List<ReportTargetEnum> reportTargetList = Arrays.stream(manuscript.getReportTarget().split(",")).map(ReportTargetEnum::valueOf).collect(Collectors.toList());
            manuscriptVo.setReportTarget(reportTargetList);
        }

        // 稿件正文
        ManuscriptContent manuscriptContent = manuscriptContentMapper.getManuscriptContent(id);
        if (ObjectUtil.isNotNull(manuscriptContent)) {
            manuscriptVo.setOriginContent(manuscriptContent.getOriginContent());
            manuscriptVo.setContent(manuscriptContent.getLatestContent());
        }



        // 反映人
        List<ManuscriptReflectorVo> reflectorList = manuscriptReflectorService.getManuscriptReflectorList(id);
        manuscriptVo.setReflectorList(reflectorList);

        // 附件列表
        List<AnnexVo> annexList = manuscriptAnnexService.getAnnexList(id);
        manuscriptVo.setAnnexList(annexList);
        return manuscriptVo;
    }

    @Override
    public IPage<ManuscriptReviewVo> getAuditPage(ManuscriptPageParamVo pageParam) {
        Page<ManuscriptReviewVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());
        Page<Manuscript> auditPage = manuscriptMapper.getAuditPage(page, pageParam);
        return ManuscriptConverter.INSTANCE.convert(auditPage);
    }

    @Override
    public IPage<ManuscriptPageVo> getMyPage(ManuscriptPageParamVo pageParam) {
        LoginUser loginUser = SecurityUtils.getLoginUser();


        LambdaQueryWrapper<Manuscript> queryWrapper = new LambdaQueryWrapper<Manuscript>()
                .eq(ObjectUtil.isNotEmpty(pageParam.getYear()), Manuscript::getYear, pageParam.getYear())
                .eq(ObjectUtil.isNotEmpty(pageParam.getCategory()), Manuscript::getCategory, pageParam.getCategory())
                .like(ObjectUtil.isNotEmpty(pageParam.getTitle()), Manuscript::getTitle, pageParam.getTitle())
                .like(Manuscript::getReflector, loginUser.getUser().getUserName())
                .or()
                .eq(Manuscript::getCreateBy, loginUser.getUser().getUserName())
                .orderByDesc(Manuscript::getCreateTime);

        if (ObjectUtil.isNotEmpty(pageParam.getStatus())) {
            if (pageParam.getStatus().equals(ManuscriptStatusEnum.NOT_ADOPTED)) {
                queryWrapper.eq(Manuscript::getAdoptWay, AdoptWayEnum.NOT_ADOPT);
            } else if (pageParam.getStatus().equals(ManuscriptStatusEnum.ADOPTED)){
                queryWrapper.eq(Manuscript::getAdoptWay, AdoptWayEnum.SINGLE_ADOPT)
                        .or()
                        .eq(Manuscript::getAdoptWay, AdoptWayEnum.COMPREHENSIVE_ADOPT);
            } else {
                queryWrapper.eq(Manuscript::getStatus, pageParam.getStatus());
            }
        }

        Page<Manuscript> manuscriptPage = manuscriptMapper.selectPage(new Page<Manuscript>(pageParam.getCurrentPage(), pageParam.getPageSize()),
                queryWrapper);

        Page<ManuscriptPageVo> pageResult = ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);
        if (ObjectUtil.isNotEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(record -> {
                if (ObjectUtil.isNotNull(record.getStatus())) {
                    if (ObjectUtil.isNotNull(record.getAdoptWay()) && record.getAdoptWay().equals(AdoptWayEnum.NOT_ADOPT)) {
                        record.setStatusLabel("未采用");
                    } else {
                        record.setStatusLabel(STATUS_LABEL_MAP.getOrDefault(
                                record.getStatus(),
                                null
                        ));
                    }
                }

            });
        }
        return pageResult;
    }

    @Override
    public IPage<ManuscriptPageVo> getPostProcessPage(ManuscriptPageParamVo pageParam) {
        Page<Manuscript> manuscriptPage = manuscriptMapper.selectManuscriptPage(pageParam);
        return ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);
    }

    @Override
    public IPage<ManuscriptPageVo> getPage(ManuscriptPageParamVo pageParam) {
        List<ManuscriptStatusEnum> statusList = Arrays.asList(ManuscriptStatusEnum.DISCARD, ManuscriptStatusEnum.AUDITING, ManuscriptStatusEnum.WAIT_FOR_ISSUE,
                ManuscriptStatusEnum.ISSUED, ManuscriptStatusEnum.FINISHED);

        Set<String> manuscriptIds = null;
        if (ObjectUtil.isNotEmpty(pageParam.getUserId())) {
            manuscriptIds = manuscriptReflectorService.getUserManuscriptIds(pageParam.getUserId());
            if (ObjectUtil.isEmpty(manuscriptIds)) {
                return new Page<>();
            }
        }

        LambdaQueryWrapper<Manuscript> queryWrapper = new LambdaQueryWrapper<Manuscript>()
                .eq(ObjectUtil.isNotEmpty(pageParam.getYear()), Manuscript::getYear, pageParam.getYear())
                .eq(ObjectUtil.isNotEmpty(pageParam.getCategory()), Manuscript::getCategory, pageParam.getCategory())
                .like(ObjectUtil.isNotEmpty(pageParam.getTitle()), Manuscript::getTitle, pageParam.getTitle())
                .like(ObjectUtil.isNotEmpty(pageParam.getReflector()), Manuscript::getReflector, pageParam.getReflector())
                .like(ObjectUtil.isNotEmpty(pageParam.getReflectUnit()), Manuscript::getReflectUnit, pageParam.getReflectUnit())
                .eq(ObjectUtil.isNotNull(pageParam.getStatus()), Manuscript::getStatus, pageParam.getStatus())
                .ge(ObjectUtil.isNotNull(pageParam.getSubmissionStartTime()), Manuscript::getSubmissionTime, pageParam.getSubmissionStartTime())
                .le(ObjectUtil.isNotNull(pageParam.getSubmissionEndTime()), Manuscript::getSubmissionTime, pageParam.getSubmissionEndTime())
                .in(ObjectUtil.isNull(pageParam.getStatus()), Manuscript::getStatus, statusList)
                .in(ObjectUtil.isNotNull(manuscriptIds), Manuscript::getId, manuscriptIds)
                .orderByDesc(Manuscript::getCreateTime);

        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (!SecurityUtils.isAdmin(user.getRoles())) {
            queryWrapper.eq(Manuscript::getIsPublish, true);
        }

        Page<Manuscript> manuscriptPage = manuscriptMapper.selectPage(new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize()), queryWrapper);

        Page<ManuscriptPageVo> pageResult = ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);
        if (ObjectUtil.isNotEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(record -> {
                record.setStatusLabel(record.getStatus().getDescription());
            });
        }

        return pageResult;
    }

    @Override
    public IPage<ManuscriptPageVo> getIssuePage(ManuscriptPageParamVo pageParam) {
        pageParam.setStatus(ManuscriptStatusEnum.WAIT_FOR_ISSUE);
        Page<Manuscript> manuscriptPage = manuscriptMapper.selectManuscriptPage(pageParam);
        return ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);
    }

    @Override
    public IPage<ManuscriptPageVo> getFeedbackPage(ManuscriptPageParamVo pageParam) {
        Page<Manuscript> manuscriptPage = manuscriptMapper.selectManuscriptPage(pageParam);
        Page<ManuscriptPageVo> pageResult = ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);

        if (ObjectUtil.isNotEmpty(pageResult.getRecords())) {
            List<String> ids = pageResult.getRecords().stream()
                    .filter(ManuscriptPageVo::getIsFeedback)
                    .map(ManuscriptPageVo::getId)
                    .collect(Collectors.toList());
            Map<String, String> feedbackMap = Collections.emptyMap();
            if (ObjectUtil.isNotEmpty(ids)) {
                feedbackMap = manuscriptFeedbackMapper.selectFeedback(ids);
            }

            for (ManuscriptPageVo record : pageResult.getRecords()) {
                String feedback = feedbackMap.get(record.getId());
                record.setFeedback(feedback);
                record.setReportUnit(getReportUnit(record.getReportUnit()));
            }

        }

        return pageResult;
    }

    @Override
    public IPage<ManuscriptPageVo> getEndorsedPage(ManuscriptPageParamVo pageParam) {
        Page<Manuscript> manuscriptPage = manuscriptMapper.selectManuscriptPage(pageParam);
        Page<ManuscriptPageVo> pageResult = ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);

        if (ObjectUtil.isNotEmpty(pageResult.getRecords())) {
            List<String> ids = pageResult.getRecords().stream()
                    .filter(ManuscriptPageVo::getIsEndorsed)
                    .map(ManuscriptPageVo::getId)
                    .collect(Collectors.toList());
            Map<String, ManuscriptEndorsement> endorsementMap = Collections.emptyMap();
            if (ObjectUtil.isNotEmpty(ids)) {
                endorsementMap = manuscriptEndorsementMapper.selectEndorsement(ids);
            }

            for (ManuscriptPageVo record : pageResult.getRecords()) {
                ManuscriptEndorsement endorsement = endorsementMap.get(record.getId());
                if (ObjectUtil.isNotEmpty(endorsement)) {
                    record.setEndorsement(endorsement.getEndorsement());
                    record.setEndorseLeader(endorsement.getCreateBy());
                }
                record.setReportUnit(getReportUnit(record.getReportUnit()));
            }
        }

        return pageResult;
    }

    @Override
    public IPage<ManuscriptPageVo> getPublicPage(ManuscriptPageParamVo pageParam) {
        Page<Manuscript> manuscriptPage = manuscriptMapper.selectManuscriptPage(pageParam);
        return ManuscriptConverter.INSTANCE.convertToPage(manuscriptPage);
    }

    @Override
    public Integer publish(@Valid ManuscriptPublishDTO publishDTO) {
        return manuscriptMapper.changeManuscriptPublishStatus(publishDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditManuscript(ManuscriptAuditDTO auditDTO) {

        auditDTO.getManuscriptEditVo().setStatus(ManuscriptStatusEnum.WAIT_FOR_ISSUE);
        this.updateManuscript(auditDTO.getManuscriptEditVo());

        ManuscriptRecord manuscriptRecord = new ManuscriptRecord();
        manuscriptRecord.setManuscriptId(auditDTO.getManuscriptId());
        manuscriptRecord.setAuditPhase(AuditPhaseEnum.INITIAL_REVIEW.getDescription());
        manuscriptRecord.setAuditOpinion(auditDTO.getAuditOpinion());
        manuscriptRecordMapper.insert(manuscriptRecord);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean issueManuscript(ManuscriptAuditDTO auditDTO) {

        auditDTO.getManuscriptEditVo().setStatus(ManuscriptStatusEnum.ISSUED);
        this.updateManuscript(auditDTO.getManuscriptEditVo());

        ManuscriptRecord manuscriptRecord = new ManuscriptRecord();
        manuscriptRecord.setManuscriptId(auditDTO.getManuscriptId());
        manuscriptRecord.setAuditPhase(AuditPhaseEnum.SIGN_OFF.getDescription());
        manuscriptRecord.setAuditOpinion(auditDTO.getAuditOpinion());
        manuscriptRecordMapper.insert(manuscriptRecord);
        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processEditManuscript(ManuscriptAuditDTO auditDTO) {

        Manuscript manuscript = manuscriptMapper.selectById(auditDTO.getManuscriptId());
        if (ObjectUtil.isNull(manuscript)) {
            return false;
        }

        ManuscriptRecord manuscriptRecord = new ManuscriptRecord();
        manuscriptRecord.setManuscriptId(auditDTO.getManuscriptId());

        if (manuscript.getStatus().equals(ManuscriptStatusEnum.AUDITING)) {
            auditDTO.getManuscriptEditVo().setStatus(ManuscriptStatusEnum.RETURN_MODIFY);
            manuscriptRecord.setAuditPhase(AuditPhaseEnum.INITIAL_REVIEW.getDescription());
        }

        if (manuscript.getStatus().equals(ManuscriptStatusEnum.WAIT_FOR_ISSUE)) {
            auditDTO.getManuscriptEditVo().setStatus(ManuscriptStatusEnum.AUDITING);
            manuscriptRecord.setAuditPhase(AuditPhaseEnum.SIGN_OFF.getDescription());
        }


        manuscriptRecord.setAuditOpinion(auditDTO.getAuditOpinion());
        manuscriptRecordMapper.insert(manuscriptRecord);
        this.updateManuscript(auditDTO.getManuscriptEditVo());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean discardOrMaterialize(ManuscriptAuditDTO auditDTO) {

        ManuscriptRecord manuscriptRecord = new ManuscriptRecord();
        manuscriptRecord.setManuscriptId(auditDTO.getManuscriptId());
        manuscriptRecord.setAuditPhase(AuditPhaseEnum.INITIAL_REVIEW.getDescription());
        manuscriptRecord.setAuditOpinion(auditDTO.getAuditOpinion());

        manuscriptRecordMapper.insert(manuscriptRecord);
        this.updateManuscript(auditDTO.getManuscriptEditVo());
        return true;
    }

    @Override
    public List<ManuscriptRecordVo> getAuditLog(String manuscriptId) {
        List<ManuscriptRecord> recordList = manuscriptRecordMapper.getManuscriptRecordList(manuscriptId);
        return ManuscriptConverter.INSTANCE.convert(recordList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateFeedback(ManuscriptResponseDTO responseDTO) {

        if (manuscriptFeedbackMapper.existFeedback(responseDTO.getManuscriptId())) {
            manuscriptFeedbackMapper.updateFeedback(responseDTO);
        } else {
            ManuscriptFeedback manuscriptFeedback = new ManuscriptFeedback();
            manuscriptFeedback.setManuscriptId(responseDTO.getManuscriptId());
            manuscriptFeedback.setFeedback(responseDTO.getText());
            manuscriptFeedbackMapper.insert(new ManuscriptFeedback());
        }



        manuscriptMapper.update(null, new LambdaUpdateWrapper<Manuscript>()
                .set(Manuscript::getIsFeedback, true)
                .set(Manuscript::getStatus, ManuscriptStatusEnum.FINISHED)
                .eq(Manuscript::getId, responseDTO.getManuscriptId()));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateEndorsement(ManuscriptResponseDTO responseDTO) {

        if (manuscriptEndorsementMapper.existEndorsement(responseDTO.getManuscriptId())) {
            manuscriptEndorsementMapper.updateEndorsement(responseDTO);
        } else {
            ManuscriptEndorsement manuscriptEndorsement = new ManuscriptEndorsement();
            manuscriptEndorsement.setManuscriptId(responseDTO.getManuscriptId());
            manuscriptEndorsement.setEndorsement(responseDTO.getText());
            manuscriptEndorsementMapper.insert(manuscriptEndorsement);
        }

        manuscriptMapper.update(null, new LambdaUpdateWrapper<Manuscript>()
                .set(Manuscript::getIsEndorsed, true)
                .set(Manuscript::getStatus, ManuscriptStatusEnum.FINISHED)
                .eq(Manuscript::getId, responseDTO.getManuscriptId()));
        return true;
    }

    @Override
    public Boolean editFeedback(ManuscriptResponseDTO responseDTO) {
        manuscriptFeedbackMapper.updateFeedback(responseDTO);
        return true;
    }

    @Override
    public Boolean editEndorsement(ManuscriptResponseDTO responseDTO) {
        manuscriptEndorsementMapper.updateEndorsement(responseDTO);
        return true;
    }

    @Override
    public String getFeedback(String manuscriptId) {
        ManuscriptFeedback manuscriptFeedback = manuscriptFeedbackMapper.getManuscriptFeedback(manuscriptId);
        return manuscriptFeedback.getFeedback();
    }

    @Override
    public String getEndorsement(String manuscriptId) {
        ManuscriptEndorsement manuscriptEndorsement = manuscriptEndorsementMapper.getManuscriptEndorsement(manuscriptId);
        return manuscriptEndorsement.getEndorsement();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDiscard(List<String> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return true;
        }
        List<ManuscriptRecord> recordList = new ArrayList<>();
        for (String id : ids) {
            ManuscriptRecord record = new ManuscriptRecord();
            record.setManuscriptId(id);
            record.setAuditPhase(AuditPhaseEnum.POST_PROCESSING.getDescription());
            record.setAuditOpinion("后期处理弃稿");
            recordList.add(record);
        }
        manuscriptRecordMapper.saveBatchRecord(recordList);
        manuscriptMapper.batchDiscard(ids);
        return true;
    }

    @Override
    public Boolean batchDelete(List<String> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return true;
        }

        manuscriptMapper.deleteBatchIds(ids);

        return true;
    }

    @Override
    public Map<String, Object> getExportVo(String manuscriptId) {
        Manuscript manuscript = manuscriptMapper.selectById(manuscriptId);
        ManuscriptExportVo exportVo = ManuscriptConverter.INSTANCE.convertToExportVo(manuscript);
//        exportVo.setSubmissionTime(DateUtil.format(manuscript.getSubmissionTime(), "yyyy-MM-dd"));
        // 处理正文
        ManuscriptContent manuscriptContent = manuscriptContentMapper.getManuscriptContent(manuscriptId);
        if (ObjectUtil.isNotEmpty(manuscriptContent)) {
            String content = manuscriptContent.getLatestContent()
                    .replaceAll("<p>", "\t")
                    .replaceAll("</p>", "\r\n")
                    .replaceAll("<br/>", "\r\n");
            String formatText = HtmlUtil.unescape(content);
            exportVo.setContent(formatText);
        }
        // 反映人
        if (ObjectUtil.isNotEmpty(manuscript.getReflector())) {
            Arrays.stream(manuscript.getReflector().split(","))
                    .findFirst()
                    .ifPresent(exportVo::setReflector);
        }

        return BeanUtil.beanToMap(exportVo);
    }

    @Override
    public List<Map<String, Object>> selectExportList(List<String> idList) {
        List<Manuscript> manuscriptList = manuscriptMapper.selectBatchIds(idList);
        List<ManuscriptContent> manuscriptContents = manuscriptContentMapper.selectBatchManuscriptIds(idList);

        Map<String, String> contentMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(manuscriptContents)) {
            manuscriptContents.forEach(content -> {
                String contentText = content.getLatestContent()
                        .replaceAll("<p>", "\t")
                        .replaceAll("</p>", "\r\n")
                        .replaceAll("<br/>", "\r\n");
                String formatText = HtmlUtil.unescape(contentText);
                contentMap.put(content.getManuscriptId(), formatText);
            });
        }


        List<ManuscriptExportVo> manuscriptExportVos = ManuscriptConverter.INSTANCE.convertToExportVo(manuscriptList);
        if (ObjectUtil.isNotEmpty(manuscriptExportVos)) {
            return manuscriptExportVos.stream().map(vo -> {
                vo.setContent(contentMap.get(vo.getId()));
                Map<String, Object> map = BeanUtil.beanToMap(vo);
                String fileName = vo.getReflector() + "_" + vo.getTitle();
                map.put("fileName", fileName);
                return map;
            }).collect(Collectors.toList());
        }

        return null;
    }

    @Override
    public Map<String, Object> selectDirectoryList(List<String> idList) {
        List<Manuscript> manuscriptList = manuscriptMapper.selectBatchIds(idList);

        List<Map<String, Object>> directoryList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(manuscriptList)) {
            int index = 1;
            for (Manuscript manuscript : manuscriptList) {
                Map<String, Object> map = new HashMap<>();
                map.put("index", index++);
                map.put("title", manuscript.getTitle());
                map.put("code", manuscript.getCode());
                map.put("issueNumber", manuscript.getIssueNumber());
                map.put("category", ObjectUtil.isNull(manuscript.getCategory()) ? Constants.BLANK_CONTENT : manuscript.getCategory().getDescription());
                map.put("submissionTime", DateUtil.format(manuscript.getSubmissionTime(), "yyyy-MM-dd"));
                map.put("reflectUnit", ObjectUtil.isEmpty(manuscript.getReflectUnit()) ? Constants.BLANK_CONTENT : manuscript.getReflectUnit());
                map.put("reflector", ObjectUtil.isEmpty(manuscript.getReflector()) ? Constants.BLANK_CONTENT : manuscript.getReflector());
                map.put("recipient", manuscript.getRecipient());
                map.put("ccUnit", manuscript.getCcUnit());
                map.put("status", manuscript.getStatus().getDescription());
                directoryList.add(map);
            }
        }

        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("exportList", directoryList);
        return dataMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String proposalToManuscript(ProposalToManuscriptDto toManuscriptDto) {
        Manuscript manuscript = new Manuscript();

        manuscript.setYear(Math.toIntExact(toManuscriptDto.getYear()));
        manuscript.setTitle(toManuscriptDto.getCaseReason());
        manuscript.setRemark(toManuscriptDto.getRemark());
        manuscript.setSource(SourceEnum.PROPOSAL_TRANSFER);
        manuscript.setSubmissionTime(toManuscriptDto.getRegisterDate());

        SysUser contactUser = sysUserService.selectUserByUserName(toManuscriptDto.getCreateBy());
        if (ObjectUtil.isNotEmpty(contactUser)) {
            manuscript.setContact(String.valueOf(contactUser.getUserId()));
        }

        List<String> reflectorList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(toManuscriptDto.getProposerList())) {
            String reflector = toManuscriptDto.getProposerList()
                    .stream()
                    .map(ProposalUserRelVo::getUserName)
                    .collect(Collectors.joining(","));
            reflectorList = toManuscriptDto.getProposerList().stream()
                    .map(proposer -> String.valueOf(proposer.getId()))
                    .collect(Collectors.toList());
            manuscript.setReflector(reflector);
        }

        manuscript.setCategory(CategoryEnum.getByName(toManuscriptDto.getCaseType()));
        manuscript.setCategoryDetail(CategoryDetailEnum.getByName(toManuscriptDto.getCaseCategory()));
        manuscript.setStatus(ManuscriptStatusEnum.AUDITING);
        manuscript.setIsPublish(toManuscriptDto.getIsOpen());

        String reportUnitDetail = JSON.toJSONString(new ReportUnitVo(), JSONWriter.Feature.WriteNulls);
        manuscript.setReportUnitDetail(reportUnitDetail);
        manuscriptMapper.insert(manuscript);

        // 反映人
        manuscriptReflectorService.saveBatchManuscriptReflector(manuscript.getId(), reflectorList);
        // 稿件正文
        ManuscriptContent manuscriptContent = new ManuscriptContent();
        manuscriptContent.setManuscriptId(manuscript.getId());
        manuscriptContent.setOriginContent(toManuscriptDto.getCaseContent());
        manuscriptContent.setLatestContent(toManuscriptDto.getCaseContent());
        manuscriptContentMapper.insert(manuscriptContent);

        // 稿件附件
        if (ObjectUtil.isNotEmpty(toManuscriptDto.getAnnexIdList())) {
            List<String> manuscriptAnnexList = toManuscriptDto.getAnnexIdList()
                    .stream()
                    .map(AnnexVo::getId).collect(Collectors.toList());
            manuscriptAnnexService.saveBatchManuscriptAnnex(manuscript.getId(), manuscriptAnnexList);
        }

        return manuscript.getId();
    }

    @Override
    public List<ManuscriptStatisticsVo> selectManuscriptStatistics(ManuscriptStatisticsDto statisticsDto) {

        if (statisticsDto.getYear() == null) {
            statisticsDto.setYear(DateUtil.thisYear());
        }

        List<CommitteeMember> committeeMembers = committeeMemberMapper.selectList(new QueryWrapper<CommitteeMember>()
                .select("user_name", "user_id").lambda()
                .eq(CommitteeMember::getYear, statisticsDto.getYear())
                .eq(ObjectUtil.isNotEmpty(statisticsDto.getSector()), CommitteeMember::getSector, statisticsDto.getSector())
                .like(ObjectUtil.isNotEmpty(statisticsDto.getUserName()), CommitteeMember::getUserName, statisticsDto.getUserName()));

        List<ManuscriptStatisticsVo> statisticsVos = manuscriptMapper.selectManuscriptStatistics(statisticsDto);
        Map<String, ManuscriptStatisticsVo> collect = statisticsVos.stream()
                .collect(Collectors.toMap(ManuscriptStatisticsVo::getUserId, Function.identity()));

        List<ManuscriptStatisticsVo> result = committeeMembers.stream()
                .map(committeeMember -> {
                    ManuscriptStatisticsVo item = new ManuscriptStatisticsVo();
                    item.setUserId(committeeMember.getUserId());
                    item.setUserName(committeeMember.getUserName());

                    ManuscriptStatisticsVo stats = collect.get(committeeMember.getUserId());
                    if (stats != null) {
                        item.setAloneCount(stats.getAloneCount());
                        item.setJointCount(stats.getJointCount());
                    }

                    item.setTotalCount();
                    return item;
                })
                .sorted(Comparator.comparing(ManuscriptStatisticsVo::getTotalCount).reversed())
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public List<ManuscriptExportVo> selectManuscriptByReflector(ManuscriptStatisticsDto statisticsDto) {

        if (statisticsDto.getYear() == null) {
            statisticsDto.setYear(DateUtil.thisYear());
        }

        Set<String> manuscriptIds = manuscriptReflectorService.getUserManuscriptIds(statisticsDto.getUserId());
        if (ObjectUtil.isEmpty(manuscriptIds)) {
            return Collections.emptyList();
        }

        List<Manuscript> manuscripts = manuscriptMapper.selectList(new LambdaQueryWrapper<Manuscript>()
                .eq(Manuscript::getYear, statisticsDto.getYear())
                .in(Manuscript::getId, manuscriptIds));

        return ManuscriptConverter.INSTANCE.convertToExportVo(manuscripts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean toAuditing(String manuscriptId) {

        manuscriptMapper.changeToAuditing(manuscriptId);

        ManuscriptRecord manuscriptRecord = new ManuscriptRecord();
        manuscriptRecord.setManuscriptId(manuscriptId);
        manuscriptRecord.setAuditPhase(AuditPhaseEnum.POST_PROCESSING.getDescription());
        manuscriptRecord.setAuditOpinion("后期处理转审核");
        manuscriptRecordMapper.insert(manuscriptRecord);
        return true;
    }

    private void updateManuscript(ManuscriptEditVo manuscriptEditVo) {
        Manuscript updateObj = ManuscriptConverter.INSTANCE.convert(manuscriptEditVo);

        validate(manuscriptEditVo.getId());

        if (ObjectUtil.isNotEmpty(manuscriptEditVo.getReportUnit())) {
            String reportUnit = manuscriptEditVo.getReportUnit().stream().map(ReportUnitEnum::name).collect(Collectors.joining(","));
            updateObj.setReportUnit(reportUnit);
        }

        if (ObjectUtil.isNotEmpty(manuscriptEditVo.getReportUnitDetail())) {
            String reportUnitDetail = JSON.toJSONString(manuscriptEditVo.getReportUnitDetail());
            updateObj.setReportUnitDetail(reportUnitDetail);
        }

        if (ObjectUtil.isNotEmpty(manuscriptEditVo.getReportTarget())) {
            String reportTarget = manuscriptEditVo.getReportTarget().stream()
                    .map(ReportTargetEnum::name)
                    .collect(Collectors.joining(","));
            updateObj.setReportTarget(reportTarget);
        }
        if (ObjectUtil.isNotEmpty(manuscriptEditVo.getContent())) {
            manuscriptContentMapper.updateManuscriptContent(manuscriptEditVo);
        }
        manuscriptMapper.updateById(updateObj);
    }

    private String getReportUnit(String reportUnit) {
        if (ObjectUtil.isNotEmpty(reportUnit)) {
            return Arrays.stream(reportUnit.split(","))
                    .map(s -> {
                        try {
                            return ReportUnitEnum.valueOf(s).getDescription();
                        } catch (IllegalArgumentException e) {
                            return null;
                        }
                    })
                    .filter(java.util.Objects::nonNull)
                    .collect(Collectors.joining(","));
        }

        return null;
    }
}
